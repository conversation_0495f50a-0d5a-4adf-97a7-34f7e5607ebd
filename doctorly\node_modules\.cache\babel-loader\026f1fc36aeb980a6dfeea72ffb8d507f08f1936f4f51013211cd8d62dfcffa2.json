{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\App.js\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 3\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "App", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/App.js"], "sourcesContent": ["import './App.css';\n\nfunction App() {\n\treturn (\n\t\t<div className=\"App\">\n\n\t\t</div>\n  \t);\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACd,oBACCD,OAAA;IAAKE,SAAS,EAAC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEf,CAAC;AAER;AAACC,EAAA,GANQN,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}