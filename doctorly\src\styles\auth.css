.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
  padding: 20px;
}

.auth-card {
  display: flex;
  width: 900px;
  min-height: 600px;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.register-card {
  min-height: 680px;
}

.auth-left {
  flex: 0 0 40%;
  background: linear-gradient(135deg, #4a90e2 0%, #1e5799 100%);
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.auth-logo h2 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.auth-illustration {
  height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIiBmaWxsPSJub25lIj48cGF0aCBkPSJNMTAwIDIwYzQ0LjE4MyAwIDgwIDM1LjgxNyA4MCA4MHMtMzUuODE3IDgwLTgwIDgwLTgwLTM1LjgxNy04MC04MCAzNS44MTctODAgODAtODB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTEwMCA1MGMxNi41NjkgMCAzMCAxMy40MzEgMzAgMzBzLTEzLjQzMSAzMC0zMCAzMC0zMC0xMy40MzEtMzAtMzAgMTMuNDMxLTMwIDMwLTMweiIgZmlsbD0iI2ZmZiIgZmlsbC1vcGFjaXR5PSIuMyIvPjxwYXRoIGQ9Ik02MCAxMzBjMC0xNi41NjkgMTMuNDMxLTMwIDMwLTMwaDIwYzE2LjU2OSAwIDMwIDEzLjQzMSAzMCAzMHYxMEg2MHYtMTB6IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9Ii4zIi8+PC9zdmc+');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.auth-tagline {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  margin-top: 20px;
}

.auth-form-container {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.auth-form-container h1 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
}

.auth-subtitle {
  color: #666;
  margin-bottom: 30px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.forgot-password {
  color: #4a90e2;
  text-decoration: none;
  font-size: 14px;
}

.auth-button {
  background: linear-gradient(135deg, #4a90e2 0%, #1e5799 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
}

.auth-button:hover {
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.auth-redirect {
  text-align: center;
  margin-top: 30px;
  color: #666;
}

.auth-redirect a {
  color: #4a90e2;
  text-decoration: none;
  font-weight: 600;
}

.role-selection {
  margin-top: 10px;
}

.role-options {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.role-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-option.active {
  border-color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.1);
}

.role-option input {
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auth-card {
    flex-direction: column;
    width: 100%;
    max-width: 500px;
  }
  
  .auth-left {
    padding: 30px;
  }
  
  .auth-illustration {
    height: 150px;
  }
}