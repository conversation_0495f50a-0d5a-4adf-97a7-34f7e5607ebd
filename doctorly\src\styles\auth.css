/* Modern Authentication Styles */
@import url('./fonts.css');
@import url('./colors.css');

/* Container and Layout */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--gradient-aurora);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  animation: aurora 20s ease-in-out infinite;
}

@keyframes aurora {
  0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
  50% { opacity: 0.8; transform: scale(1.1) rotate(1deg); }
}

/* Card Styles */
.auth-card {
  display: flex;
  width: 1000px;
  min-height: 650px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32px;
  overflow: hidden;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.register-card {
  min-height: 750px;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: -1;
}

/* Left Panel Styles */
.auth-left {
  flex: 0 0 45%;
  color: white;
  padding: 48px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.auth-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 30px 30px, 50px 50px;
  animation: float 25s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(20px, -20px) rotate(90deg); }
  50% { transform: translate(-10px, 30px) rotate(180deg); }
  75% { transform: translate(-30px, -10px) rotate(270deg); }
}

/* Logo Styles */
.auth-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 2;
  position: relative;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.logo-icon:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.logo-icon svg {
  width: 28px;
  height: 28px;
}

/* Illustration Styles */
.auth-illustration {
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.medical-scene, .registration-scene {
  position: relative;
  width: 240px;
  height: 240px;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.medical-card {
  position: absolute;
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.medical-card svg {
  width: 36px;
  height: 36px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.medical-card.card-1 {
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.medical-card.card-2 {
  bottom: 30px;
  left: 20px;
}

.medical-card.card-3 {
  bottom: 30px;
  right: 20px;
}

.pulse-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.pulse-ring {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 3s ease-out infinite;
}

.pulse-ring.ring-1 {
  width: 80px;
  height: 80px;
  margin: -40px 0 0 -40px;
  animation-delay: 0s;
}

.pulse-ring.ring-2 {
  width: 120px;
  height: 120px;
  margin: -60px 0 0 -60px;
  animation-delay: 1s;
}

.pulse-ring.ring-3 {
  width: 160px;
  height: 160px;
  margin: -80px 0 0 -80px;
  animation-delay: 2s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* Tagline Styles */
.auth-tagline {
  text-align: center;
  margin-top: 24px;
  position: relative;
  z-index: 2;
}

.tagline-accent {
  width: 60px;
  height: 3px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 2px;
  margin: 12px auto 0;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
  to { box-shadow: 0 0 15px rgba(255, 255, 255, 0.6); }
}

/* Form Container */
.auth-form-container {
  flex: 1;
  padding: 48px;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.02);
}

.auth-subtitle {
  margin-bottom: 40px;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.register-form {
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .floating-input-group {
  flex: 1;
}

/* Floating Input Styles */
.floating-input-group {
  position: relative;
  margin-bottom: 24px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon-wrapper {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  transition: all 0.3s ease;
}

.input-icon {
  width: 20px;
  height: 20px;
  color: var(--color-neutral-400);
  transition: color 0.3s ease;
}

.floating-input {
  width: 100%;
  padding: 20px 16px 20px 52px;
  border: 2px solid var(--color-neutral-200);
  border-radius: 16px;
  font-size: 16px;
  font-family: var(--font-primary);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.floating-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(20, 184, 166, 0.1);
}

.floating-input:focus + .floating-label,
.floating-input:not(:placeholder-shown) + .floating-label {
  transform: translateY(-32px) scale(0.85);
  color: var(--color-primary-600);
  background: rgba(255, 255, 255, 0.9);
  padding: 0 8px;
}

.floating-input:focus ~ .input-icon-wrapper .input-icon {
  color: var(--color-primary-500);
}

.floating-label {
  position: absolute;
  left: 52px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 2;
  border-radius: 4px;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 1px;
}

.floating-input:focus ~ .input-border {
  transform: scaleX(1);
}

/* Password Toggle */
.password-toggle-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  z-index: 3;
}

.password-toggle-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.password-toggle-btn svg {
  width: 20px;
  height: 20px;
  color: var(--color-neutral-500);
  transition: color 0.2s ease;
}

.password-toggle-btn:hover svg {
  color: var(--color-primary-600);
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0 24px 0;
}

/* Custom Checkbox */
.custom-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  position: relative;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-neutral-300);
  border-radius: 6px;
  position: relative;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label {
  user-select: none;
}

.forgot-password {
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.forgot-password::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary-500);
  transition: width 0.3s ease;
}

.forgot-password:hover::after {
  width: 100%;
}

/* Modern Button Styles */
.modern-auth-button {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 18px 32px;
  font-family: var(--font-accent);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(20, 184, 166, 0.25);
}

.modern-auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-auth-button:hover::before {
  left: 100%;
}

.modern-auth-button:hover:not(:disabled) {
  transform: translateY(-2px);
}

.modern-auth-button:active:not(:disabled) {
  transform: translateY(0);
}

.modern-auth-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

.modern-auth-button.loading {
  pointer-events: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.button-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

.modern-auth-button:hover .button-icon {
  transform: translateX(2px);
}

.modern-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: modernSpin 1s linear infinite;
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer and Social Login */
.auth-footer {
  margin-top: 32px;
  text-align: center;
}

.auth-redirect {
  margin-bottom: 24px;
}

.auth-link {
  text-decoration: none;
  position: relative;
  transition: all 0.2s ease;
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary-500);
  transition: width 0.3s ease;
}

.auth-link:hover::after {
  width: 100%;
}

.social-login {
  margin-top: 24px;
}

.divider {
  position: relative;
  margin: 24px 0;
  text-align: center;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--color-neutral-200);
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 16px;
  position: relative;
  z-index: 1;
}

.social-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.social-btn {
  width: 48px;
  height: 48px;
  border: 2px solid var(--color-neutral-200);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.social-btn:hover {
  border-color: var(--color-primary-300);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-btn svg {
  width: 24px;
  height: 24px;
}

/* Role Selection Styles */
.role-selection {
  margin-top: 16px;
}

.role-options {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.role-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid var(--color-neutral-200);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.role-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(20, 184, 166, 0.1), transparent);
  transition: left 0.5s ease;
}

.role-option:hover::before {
  left: 100%;
}

.role-option.active {
  border-color: var(--color-primary-500);
  background: rgba(20, 184, 166, 0.05);
  box-shadow: 0 8px 25px rgba(20, 184, 166, 0.15);
}

.role-icon {
  width: 48px;
  height: 48px;
  background: rgba(20, 184, 166, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.role-option.active .role-icon {
  background: rgba(20, 184, 166, 0.2);
  transform: scale(1.1);
}

.role-icon svg {
  width: 24px;
  height: 24px;
  color: var(--color-primary-600);
}

.role-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.role-content input {
  display: none;
}

.role-content label {
  font-family: var(--font-accent);
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  cursor: pointer;
}

.role-description {
  font-size: 13px;
  color: var(--text-muted);
  font-weight: 400;
}

/* Password Strength and Validation */
.password-strength {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background: var(--color-neutral-200);
  border-radius: 3px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.strength-fill.strength-0 { width: 0%; background: var(--color-error-500); }
.strength-fill.strength-1 { width: 20%; background: var(--color-error-500); }
.strength-fill.strength-2 { width: 40%; background: var(--color-warning-500); }
.strength-fill.strength-3 { width: 60%; background: var(--color-warning-600); }
.strength-fill.strength-4 { width: 80%; background: var(--color-success-500); }
.strength-fill.strength-5 { width: 100%; background: var(--color-success-600); }

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
}

.strength-text.strength-0,
.strength-text.strength-1 { color: var(--color-error-600); }
.strength-text.strength-2 { color: var(--color-warning-600); }
.strength-text.strength-3 { color: var(--color-warning-700); }
.strength-text.strength-4,
.strength-text.strength-5 { color: var(--color-success-600); }

.password-mismatch {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-error-600);
  font-size: 13px;
  font-weight: 500;
}

.password-mismatch svg {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .auth-card {
    width: 90%;
    max-width: 900px;
  }
}

@media (max-width: 768px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    flex-direction: column;
    width: 100%;
    max-width: 500px;
    min-height: auto;
    border-radius: 24px;
  }

  .auth-left {
    padding: 32px;
    flex: none;
    border-radius: 24px 24px 0 0;
  }

  .auth-illustration {
    height: 160px;
  }

  .medical-scene, .registration-scene {
    width: 180px;
    height: 180px;
  }

  .medical-card {
    width: 56px;
    height: 56px;
  }

  .medical-card svg {
    width: 28px;
    height: 28px;
  }

  .auth-form-container {
    padding: 32px;
  }

  .floating-input {
    padding: 18px 16px 18px 48px;
  }

  .input-icon-wrapper {
    left: 14px;
  }

  .floating-label {
    left: 48px;
  }

  .role-options {
    flex-direction: column;
    gap: 12px;
  }

  .role-option {
    padding: 16px;
  }

  .role-icon {
    width: 40px;
    height: 40px;
  }

  .role-icon svg {
    width: 20px;
    height: 20px;
  }

  .social-buttons {
    gap: 12px;
  }

  .social-btn {
    width: 44px;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 12px;
  }

  .auth-left {
    padding: 24px;
  }

  .auth-form-container {
    padding: 24px;
  }

  .auth-illustration {
    height: 120px;
  }

  .medical-scene, .registration-scene {
    width: 140px;
    height: 140px;
  }

  .medical-card {
    width: 48px;
    height: 48px;
  }

  .medical-card svg {
    width: 24px;
    height: 24px;
  }

  .floating-input {
    padding: 16px 14px 16px 44px;
    font-size: 15px;
  }

  .input-icon-wrapper {
    left: 12px;
  }

  .floating-label {
    left: 44px;
  }

  .modern-auth-button {
    padding: 16px 24px;
    font-size: 15px;
  }

  .form-row {
    flex-direction: column;
  }

  .role-option {
    padding: 14px;
  }

  .role-icon {
    width: 36px;
    height: 36px;
  }

  .role-icon svg {
    width: 18px;
    height: 18px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auth-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .floating-input {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
  }

  .floating-input:focus {
    background: rgba(0, 0, 0, 0.5);
    border-color: var(--color-primary-400);
  }

  .role-option {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .social-btn {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
  }
}