.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.auth-card {
  display: flex;
  width: 900px;
  min-height: 600px;
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.register-card {
  min-height: 680px;
}

.auth-left {
  flex: 0 0 40%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.auth-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.auth-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 2;
  position: relative;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.logo-icon svg {
  width: 24px;
  height: 24px;
}

.auth-logo h2 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.auth-illustration {
  height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.medical-icons {
  position: relative;
  width: 200px;
  height: 200px;
}

.floating-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-icon svg {
  width: 30px;
  height: 30px;
}

.floating-icon.icon-1 {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  animation: float-1 6s ease-in-out infinite;
}

.floating-icon.icon-2 {
  bottom: 20px;
  left: 20px;
  animation: float-2 8s ease-in-out infinite;
}

.floating-icon.icon-3 {
  bottom: 20px;
  right: 20px;
  animation: float-3 7s ease-in-out infinite;
}

@keyframes float-1 {
  0%, 100% { transform: translateX(-50%) translateY(0px); }
  50% { transform: translateX(-50%) translateY(-10px); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-12px) rotate(-5deg); }
}

.auth-tagline {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  margin-top: 20px;
}

.auth-form-container {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.auth-form-container h1 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
}

.auth-subtitle {
  color: #666;
  margin-bottom: 30px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.form-group input::placeholder {
  color: #9ca3af;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.password-toggle:hover {
  background-color: #f3f4f6;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.forgot-password {
  color: #4a90e2;
  text-decoration: none;
  font-size: 14px;
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.strength-fill.strength-0 { width: 0%; background: #ef4444; }
.strength-fill.strength-1 { width: 20%; background: #ef4444; }
.strength-fill.strength-2 { width: 40%; background: #f59e0b; }
.strength-fill.strength-3 { width: 60%; background: #eab308; }
.strength-fill.strength-4 { width: 80%; background: #22c55e; }
.strength-fill.strength-5 { width: 100%; background: #16a34a; }

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
}

.strength-text.strength-0,
.strength-text.strength-1 { color: #ef4444; }
.strength-text.strength-2 { color: #f59e0b; }
.strength-text.strength-3 { color: #eab308; }
.strength-text.strength-4,
.strength-text.strength-5 { color: #22c55e; }

/* Password Mismatch */
.password-mismatch {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
}

.password-mismatch svg {
  width: 14px;
  height: 14px;
}

.auth-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.auth-button:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

.auth-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-icon {
  width: 18px;
  height: 18px;
}

.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.auth-redirect {
  text-align: center;
  margin-top: 30px;
  color: #666;
}

.auth-redirect a {
  color: #4a90e2;
  text-decoration: none;
  font-weight: 600;
}

.role-selection {
  margin-top: 10px;
}

.role-options {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.role-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
  position: relative;
  overflow: hidden;
}

.role-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.role-option:hover::before {
  left: 100%;
}

.role-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.role-icon {
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.role-option.active .role-icon {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.role-icon svg {
  width: 20px;
  height: 20px;
  color: #667eea;
}

.role-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-content input {
  display: none;
}

.role-content label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  cursor: pointer;
}

.role-description {
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auth-container {
    padding: 10px;
  }

  .auth-card {
    flex-direction: column;
    width: 100%;
    max-width: 500px;
    min-height: auto;
  }

  .auth-left {
    padding: 30px;
    flex: none;
  }

  .auth-illustration {
    height: 120px;
  }

  .medical-icons {
    width: 150px;
    height: 150px;
  }

  .floating-icon {
    width: 45px;
    height: 45px;
  }

  .floating-icon svg {
    width: 22px;
    height: 22px;
  }

  .auth-form-container {
    padding: 30px;
  }

  .role-options {
    flex-direction: column;
    gap: 12px;
  }

  .role-option {
    padding: 14px;
  }

  .role-icon {
    width: 35px;
    height: 35px;
  }

  .role-icon svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .auth-left {
    padding: 20px;
  }

  .auth-form-container {
    padding: 20px;
  }

  .auth-logo h2 {
    font-size: 24px;
  }

  .auth-form-container h1 {
    font-size: 24px;
  }
}