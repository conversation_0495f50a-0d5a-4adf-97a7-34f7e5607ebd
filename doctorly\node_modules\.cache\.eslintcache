[{"E:\\projects\\doctorly\\src\\index.js": "1", "E:\\projects\\doctorly\\src\\reportWebVitals.js": "2", "E:\\projects\\doctorly\\src\\App.js": "3", "E:\\projects\\doctorly\\src\\components\\Auth\\Register.js": "4", "E:\\projects\\doctorly\\src\\components\\Auth\\Login.js": "5"}, {"size": 535, "mtime": 1752363417145, "results": "6", "hashOfConfig": "7"}, {"size": 362, "mtime": 1752363418226, "results": "8", "hashOfConfig": "7"}, {"size": 536, "mtime": 1752366825036, "results": "9", "hashOfConfig": "7"}, {"size": 5221, "mtime": 1752366922816, "results": "10", "hashOfConfig": "7"}, {"size": 2931, "mtime": 1752366915390, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1q1j8vv", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\projects\\doctorly\\src\\index.js", [], [], "E:\\projects\\doctorly\\src\\reportWebVitals.js", [], [], "E:\\projects\\doctorly\\src\\App.js", [], [], "E:\\projects\\doctorly\\src\\components\\Auth\\Register.js", [], [], "E:\\projects\\doctorly\\src\\components\\Auth\\Login.js", [], []]