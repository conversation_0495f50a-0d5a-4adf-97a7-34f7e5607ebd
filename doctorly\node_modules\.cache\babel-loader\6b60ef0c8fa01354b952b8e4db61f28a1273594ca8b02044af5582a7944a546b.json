{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\components\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Login logic will be implemented later\n    console.log('Login attempt:', {\n      email,\n      password\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-left\",\n        initial: {\n          opacity: 0,\n          x: -50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-logo\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Doctorly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-illustration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-tagline\",\n          children: \"Your health, our priority\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-form-container\",\n        initial: {\n          opacity: 0,\n          x: 50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Sign in to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              id: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"password\",\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remember-me\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"remember\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/forgot-password\",\n              className: \"forgot-password\",\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: \"auth-button\",\n            whileHover: {\n              scale: 1.03\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-redirect\",\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/register\",\n            children: \"Sign up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"TSZhDBNy8CmbxXgprY/vvMmTG1Q=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "motion", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "handleSubmit", "e", "preventDefault", "console", "log", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "required", "href", "button", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport '../../styles/auth.css';\r\n\r\nconst Login = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Login logic will be implemented later\r\n    console.log('Login attempt:', { email, password });\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card\">\r\n        <motion.div \r\n          className=\"auth-left\"\r\n          initial={{ opacity: 0, x: -50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <div className=\"auth-logo\">\r\n            <h2>Doctorly</h2>\r\n          </div>\r\n          <div className=\"auth-illustration\">\r\n            {/* Medical illustration will go here */}\r\n          </div>\r\n          <p className=\"auth-tagline\">Your health, our priority</p>\r\n        </motion.div>\r\n        \r\n        <motion.div \r\n          className=\"auth-form-container\"\r\n          initial={{ opacity: 0, x: 50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n        >\r\n          <h1>Welcome Back</h1>\r\n          <p className=\"auth-subtitle\">Sign in to continue</p>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"auth-form\">\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"email\">Email</label>\r\n              <input\r\n                type=\"email\"\r\n                id=\"email\"\r\n                value={email}\r\n                onChange={(e) => setEmail(e.target.value)}\r\n                required\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"password\">Password</label>\r\n              <input\r\n                type=\"password\"\r\n                id=\"password\"\r\n                value={password}\r\n                onChange={(e) => setPassword(e.target.value)}\r\n                required\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"form-options\">\r\n              <div className=\"remember-me\">\r\n                <input type=\"checkbox\" id=\"remember\" />\r\n                <label htmlFor=\"remember\">Remember me</label>\r\n              </div>\r\n              <a href=\"/forgot-password\" className=\"forgot-password\">Forgot password?</a>\r\n            </div>\r\n            \r\n            <motion.button \r\n              type=\"submit\" \r\n              className=\"auth-button\"\r\n              whileHover={{ scale: 1.03 }}\r\n              whileTap={{ scale: 0.98 }}\r\n            >\r\n              Sign In\r\n            </motion.button>\r\n          </form>\r\n          \r\n          <p className=\"auth-redirect\">\r\n            Don't have an account? <a href=\"/register\">Sign up</a>\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMU,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAAER,KAAK;MAAEE;IAAS,CAAC,CAAC;EACpD,CAAC;EAED,oBACEL,OAAA;IAAKY,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7Bb,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBb,OAAA,CAACF,MAAM,CAACgB,GAAG;QACTF,SAAS,EAAC,WAAW;QACrBG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAP,QAAA,gBAE9Bb,OAAA;UAAKY,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBb,OAAA;YAAAa,QAAA,EAAI;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNxB,OAAA;UAAKY,SAAS,EAAC;QAAmB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE7B,CAAC,eACNxB,OAAA;UAAGY,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAEbxB,OAAA,CAACF,MAAM,CAACgB,GAAG;QACTF,SAAS,EAAC,qBAAqB;QAC/BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,gBAE1Cb,OAAA;UAAAa,QAAA,EAAI;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBxB,OAAA;UAAGY,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEpDxB,OAAA;UAAM0B,QAAQ,EAAEnB,YAAa;UAACK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjDb,OAAA;YAAKY,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBb,OAAA;cAAO2B,OAAO,EAAC,OAAO;cAAAd,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxB,OAAA;cACE4B,IAAI,EAAC,OAAO;cACZC,EAAE,EAAC,OAAO;cACVC,KAAK,EAAE3B,KAAM;cACb4B,QAAQ,EAAGvB,CAAC,IAAKJ,QAAQ,CAACI,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAE;cAC1CG,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxB,OAAA;YAAKY,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBb,OAAA;cAAO2B,OAAO,EAAC,UAAU;cAAAd,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CxB,OAAA;cACE4B,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,UAAU;cACbC,KAAK,EAAEzB,QAAS;cAChB0B,QAAQ,EAAGvB,CAAC,IAAKF,WAAW,CAACE,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAE;cAC7CG,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxB,OAAA;YAAKY,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3Bb,OAAA;cAAKY,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1Bb,OAAA;gBAAO4B,IAAI,EAAC,UAAU;gBAACC,EAAE,EAAC;cAAU;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxB,OAAA;gBAAO2B,OAAO,EAAC,UAAU;gBAAAd,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNxB,OAAA;cAAGkC,IAAI,EAAC,kBAAkB;cAACtB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAENxB,OAAA,CAACF,MAAM,CAACqC,MAAM;YACZP,IAAI,EAAC,QAAQ;YACbhB,SAAS,EAAC,aAAa;YACvBwB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAxB,QAAA,EAC3B;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEPxB,OAAA;UAAGY,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,yBACJ,eAAAb,OAAA;YAAGkC,IAAI,EAAC,WAAW;YAAArB,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CArFID,KAAK;AAAAsC,EAAA,GAALtC,KAAK;AAuFX,eAAeA,KAAK;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}