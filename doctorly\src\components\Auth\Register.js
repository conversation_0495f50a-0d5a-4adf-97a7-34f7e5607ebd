import React, { useState } from 'react';
import { motion } from 'framer-motion';
import '../../styles/auth.css';

const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'patient'
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Registration logic will be implemented later
    console.log('Registration data:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card register-card">
        <motion.div 
          className="auth-left"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="auth-logo">
            <h2>Doctorly</h2>
          </div>
          <div className="auth-illustration">
            {/* Medical illustration will go here */}
          </div>
          <p className="auth-tagline">Join us for better healthcare</p>
        </motion.div>
        
        <motion.div 
          className="auth-form-container"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h1>Create Account</h1>
          <p className="auth-subtitle">Sign up to get started</p>
          
          <form onSubmit={handleSubmit} className="auth-form">
            <div className="form-group">
              <label htmlFor="name">Full Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group role-selection">
              <label>I am a:</label>
              <div className="role-options">
                <motion.div 
                  className={`role-option ${formData.role === 'patient' ? 'active' : ''}`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setFormData({...formData, role: 'patient'})}
                >
                  <input 
                    type="radio" 
                    id="patient" 
                    name="role" 
                    value="patient"
                    checked={formData.role === 'patient'}
                    onChange={handleChange}
                  />
                  <label htmlFor="patient">Patient</label>
                </motion.div>
                
                <motion.div 
                  className={`role-option ${formData.role === 'doctor' ? 'active' : ''}`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setFormData({...formData, role: 'doctor'})}
                >
                  <input 
                    type="radio" 
                    id="doctor" 
                    name="role" 
                    value="doctor"
                    checked={formData.role === 'doctor'}
                    onChange={handleChange}
                  />
                  <label htmlFor="doctor">Doctor</label>
                </motion.div>
              </div>
            </div>
            
            <motion.button 
              type="submit" 
              className="auth-button"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              Create Account
            </motion.button>
          </form>
          
          <p className="auth-redirect">
            Already have an account? <a href="/login">Sign in</a>
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default Register;