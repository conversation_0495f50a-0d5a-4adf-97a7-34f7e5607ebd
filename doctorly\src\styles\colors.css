/* Modern Healthcare Color System */
:root {
  /* Primary Colors - Medical Blue/Teal Palette */
  --color-primary-50: #f0fdfa;
  --color-primary-100: #ccfbf1;
  --color-primary-200: #99f6e4;
  --color-primary-300: #5eead4;
  --color-primary-400: #2dd4bf;
  --color-primary-500: #14b8a6;
  --color-primary-600: #0d9488;
  --color-primary-700: #0f766e;
  --color-primary-800: #115e59;
  --color-primary-900: #134e4a;
  --color-primary-950: #042f2e;

  /* Secondary Colors - Warm Purple/Violet */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;
  --color-secondary-950: #3b0764;

  /* Accent Colors - Energetic Orange */
  --color-accent-50: #fff7ed;
  --color-accent-100: #ffedd5;
  --color-accent-200: #fed7aa;
  --color-accent-300: #fdba74;
  --color-accent-400: #fb923c;
  --color-accent-500: #f97316;
  --color-accent-600: #ea580c;
  --color-accent-700: #c2410c;
  --color-accent-800: #9a3412;
  --color-accent-900: #7c2d12;
  --color-accent-950: #431407;

  /* Neutral Colors - Sophisticated Grays */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;

  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;

  /* Background Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary-400) 0%, var(--color-accent-500) 100%);
  --gradient-neutral: linear-gradient(135deg, var(--color-neutral-100) 0%, var(--color-neutral-200) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Complex Gradients */
  --gradient-aurora: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --gradient-sunset: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-forest: linear-gradient(135deg, #134e5e 0%, #71b280 100%);

  /* Shadow Colors */
  --shadow-primary: rgba(20, 184, 166, 0.25);
  --shadow-secondary: rgba(168, 85, 247, 0.25);
  --shadow-neutral: rgba(0, 0, 0, 0.1);
  --shadow-glass: rgba(255, 255, 255, 0.2);

  /* Text Colors */
  --text-primary: var(--color-neutral-900);
  --text-secondary: var(--color-neutral-600);
  --text-muted: var(--color-neutral-500);
  --text-inverse: var(--color-neutral-50);
  --text-accent: var(--color-primary-600);

  /* Border Colors */
  --border-light: var(--color-neutral-200);
  --border-medium: var(--color-neutral-300);
  --border-dark: var(--color-neutral-400);
  --border-accent: var(--color-primary-300);

  /* Background Colors */
  --bg-primary: var(--color-neutral-50);
  --bg-secondary: var(--color-neutral-100);
  --bg-muted: var(--color-neutral-200);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.5);
}

/* Color Utility Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }
.text-accent { color: var(--text-accent); }

.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }
.text-info { color: var(--color-info-600); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-muted { background-color: var(--bg-muted); }
.bg-glass { background-color: var(--bg-glass); }

.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-secondary { background: var(--gradient-secondary); }
.bg-gradient-aurora { background: var(--gradient-aurora); }
.bg-gradient-ocean { background: var(--gradient-ocean); }
.bg-gradient-sunset { background: var(--gradient-sunset); }
.bg-gradient-forest { background: var(--gradient-forest); }

.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }
.border-accent { border-color: var(--border-accent); }

/* Shadow Utilities */
.shadow-primary { box-shadow: 0 10px 25px var(--shadow-primary); }
.shadow-secondary { box-shadow: 0 10px 25px var(--shadow-secondary); }
.shadow-neutral { box-shadow: 0 10px 25px var(--shadow-neutral); }
.shadow-glass { box-shadow: 0 8px 32px var(--shadow-glass); }

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: var(--color-neutral-100);
    --text-secondary: var(--color-neutral-300);
    --text-muted: var(--color-neutral-400);
    --text-inverse: var(--color-neutral-900);
    
    --bg-primary: var(--color-neutral-900);
    --bg-secondary: var(--color-neutral-800);
    --bg-muted: var(--color-neutral-700);
    --bg-glass: rgba(0, 0, 0, 0.8);
    
    --border-light: var(--color-neutral-700);
    --border-medium: var(--color-neutral-600);
    --border-dark: var(--color-neutral-500);
  }
}

/* Animation Support */
.color-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Hover States */
.hover-primary:hover { background-color: var(--color-primary-600); }
.hover-secondary:hover { background-color: var(--color-secondary-600); }
.hover-accent:hover { background-color: var(--color-accent-600); }

/* Focus States */
.focus-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.2);
  border-color: var(--color-primary-500);
}

.focus-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2);
  border-color: var(--color-secondary-500);
}
