{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\components\\\\Auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'patient'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Calculate password strength\n    if (name === 'password') {\n      calculatePasswordStrength(value);\n    }\n  };\n  const calculatePasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength += 1;\n    if (/[A-Z]/.test(password)) strength += 1;\n    if (/[a-z]/.test(password)) strength += 1;\n    if (/[0-9]/.test(password)) strength += 1;\n    if (/[^A-Za-z0-9]/.test(password)) strength += 1;\n    setPasswordStrength(strength);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    // Simulate loading\n    setTimeout(() => {\n      setIsLoading(false);\n      console.log('Registration data:', formData);\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    style: {\n      height: '100vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card register-card\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-left\",\n        initial: {\n          opacity: 0,\n          x: -50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H11V21H5V3H13V9H21ZM14 15.5L22.5 7L21 5.5L14 12.5L10.5 9L9 10.5L14 15.5Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Doctorly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-illustration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"medical-icons\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-icon icon-1\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-icon icon-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-icon icon-3\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-tagline\",\n          children: \"Join us for better healthcare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-form-container\",\n        initial: {\n          opacity: 0,\n          x: 50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Sign up to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"name\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), \"Full Name\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Enter your full name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), \"Email Address\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                placeholder: \"Enter your email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), \"Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Create a strong password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowPassword(!showPassword),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"password-strength\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"strength-bar\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `strength-fill strength-${passwordStrength}`,\n                  style: {\n                    width: `${passwordStrength / 5 * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `strength-text strength-${passwordStrength}`,\n                children: [passwordStrength === 0 && 'Very Weak', passwordStrength === 1 && 'Weak', passwordStrength === 2 && 'Fair', passwordStrength === 3 && 'Good', passwordStrength === 4 && 'Strong', passwordStrength === 5 && 'Very Strong']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), \"Confirm Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showConfirmPassword ? \"text\" : \"password\",\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                placeholder: \"Confirm your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), formData.confirmPassword && formData.password !== formData.confirmPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"password-mismatch\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), \"Passwords do not match\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group role-selection\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16,4C16.88,4 17.67,4.38 18.18,5L20.5,2.69L21.81,4L19.5,6.31C19.81,6.81 20,7.39 20,8A4,4 0 0,1 16,12A4,4 0 0,1 12,8A4,4 0 0,1 16,4M16,5.5A2.5,2.5 0 0,0 13.5,8A2.5,2.5 0 0,0 16,10.5A2.5,2.5 0 0,0 18.5,8A2.5,2.5 0 0,0 16,5.5M16,13C18.67,13 21.33,14.67 22,16.67V20H10V16.67C10.67,14.67 13.33,13 16,13M8,12A4,4 0 0,1 4,8A4,4 0 0,1 8,4A4,4 0 0,1 12,8A4,4 0 0,1 8,12M8,5.5A2.5,2.5 0 0,0 5.5,8A2.5,2.5 0 0,0 8,10.5A2.5,2.5 0 0,0 10.5,8A2.5,2.5 0 0,0 8,5.5M8,13C10.67,13 13.33,14.67 14,16.67V20H2V16.67C2.67,14.67 5.33,13 8,13Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), \"I am a:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"role-options\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: `role-option ${formData.role === 'patient' ? 'active' : ''}`,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setFormData({\n                  ...formData,\n                  role: 'patient'\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    id: \"patient\",\n                    name: \"role\",\n                    value: \"patient\",\n                    checked: formData.role === 'patient',\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"patient\",\n                    children: \"Patient\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"role-description\",\n                    children: \"Seeking medical care\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: `role-option ${formData.role === 'doctor' ? 'active' : ''}`,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setFormData({\n                  ...formData,\n                  role: 'doctor'\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    id: \"doctor\",\n                    name: \"role\",\n                    value: \"doctor\",\n                    checked: formData.role === 'doctor',\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"doctor\",\n                    children: \"Doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"role-description\",\n                    children: \"Healthcare provider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: `auth-button ${isLoading ? 'loading' : ''}`,\n            whileHover: {\n              scale: isLoading ? 1 : 1.03\n            },\n            whileTap: {\n              scale: isLoading ? 1 : 0.98\n            },\n            disabled: isLoading,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), \"Creating Account...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"button-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), \"Create Account\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-redirect\",\n          children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"UtUJfDBITmx/Cqjl7KlRCQGUxIA=\");\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "motion", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "role", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isLoading", "setIsLoading", "passwordStrength", "setPasswordStrength", "handleChange", "e", "value", "target", "calculatePasswordStrength", "strength", "length", "test", "handleSubmit", "preventDefault", "setTimeout", "console", "log", "className", "style", "height", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "required", "onClick", "width", "whileHover", "scale", "whileTap", "checked", "button", "disabled", "href", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/components/Auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport '../../styles/auth.css';\r\n\r\nconst Register = () => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    role: 'patient'\r\n  });\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [passwordStrength, setPasswordStrength] = useState(0);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n\r\n    // Calculate password strength\r\n    if (name === 'password') {\r\n      calculatePasswordStrength(value);\r\n    }\r\n  };\r\n\r\n  const calculatePasswordStrength = (password) => {\r\n    let strength = 0;\r\n    if (password.length >= 8) strength += 1;\r\n    if (/[A-Z]/.test(password)) strength += 1;\r\n    if (/[a-z]/.test(password)) strength += 1;\r\n    if (/[0-9]/.test(password)) strength += 1;\r\n    if (/[^A-Za-z0-9]/.test(password)) strength += 1;\r\n    setPasswordStrength(strength);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    // Simulate loading\r\n    setTimeout(() => {\r\n      setIsLoading(false);\r\n      console.log('Registration data:', formData);\r\n    }, 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\" style={{height: '100vh'}}>\r\n      <div className=\"auth-card register-card\">\r\n        <motion.div\r\n          className=\"auth-left\"\r\n          initial={{ opacity: 0, x: -50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <div className=\"auth-logo\">\r\n            <div className=\"logo-icon\">\r\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H11V21H5V3H13V9H21ZM14 15.5L22.5 7L21 5.5L14 12.5L10.5 9L9 10.5L14 15.5Z\"/>\r\n              </svg>\r\n            </div>\r\n            <h2>Doctorly</h2>\r\n          </div>\r\n          <div className=\"auth-illustration\">\r\n            <div className=\"medical-icons\">\r\n              <div className=\"floating-icon icon-1\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n                </svg>\r\n              </div>\r\n              <div className=\"floating-icon icon-2\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"/>\r\n                </svg>\r\n              </div>\r\n              <div className=\"floating-icon icon-3\">\r\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"/>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <p className=\"auth-tagline\">Join us for better healthcare</p>\r\n        </motion.div>\r\n        \r\n        <motion.div \r\n          className=\"auth-form-container\"\r\n          initial={{ opacity: 0, x: 50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n        >\r\n          <h1>Create Account</h1>\r\n          <p className=\"auth-subtitle\">Sign up to get started</p>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"auth-form\">\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"name\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                </svg>\r\n                Full Name\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter your full name\"\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"email\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"/>\r\n                </svg>\r\n                Email Address\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"password\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                </svg>\r\n                Password\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  value={formData.password}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Create a strong password\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"password-toggle\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              {formData.password && (\r\n                <div className=\"password-strength\">\r\n                  <div className=\"strength-bar\">\r\n                    <div\r\n                      className={`strength-fill strength-${passwordStrength}`}\r\n                      style={{ width: `${(passwordStrength / 5) * 100}%` }}\r\n                    ></div>\r\n                  </div>\r\n                  <span className={`strength-text strength-${passwordStrength}`}>\r\n                    {passwordStrength === 0 && 'Very Weak'}\r\n                    {passwordStrength === 1 && 'Weak'}\r\n                    {passwordStrength === 2 && 'Fair'}\r\n                    {passwordStrength === 3 && 'Good'}\r\n                    {passwordStrength === 4 && 'Strong'}\r\n                    {passwordStrength === 5 && 'Very Strong'}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"confirmPassword\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                </svg>\r\n                Confirm Password\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type={showConfirmPassword ? \"text\" : \"password\"}\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  value={formData.confirmPassword}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Confirm your password\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"password-toggle\"\r\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showConfirmPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              {formData.confirmPassword && formData.password !== formData.confirmPassword && (\r\n                <div className=\"password-mismatch\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\"/>\r\n                  </svg>\r\n                  Passwords do not match\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"form-group role-selection\">\r\n              <label>\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M16,4C16.88,4 17.67,4.38 18.18,5L20.5,2.69L21.81,4L19.5,6.31C19.81,6.81 20,7.39 20,8A4,4 0 0,1 16,12A4,4 0 0,1 12,8A4,4 0 0,1 16,4M16,5.5A2.5,2.5 0 0,0 13.5,8A2.5,2.5 0 0,0 16,10.5A2.5,2.5 0 0,0 18.5,8A2.5,2.5 0 0,0 16,5.5M16,13C18.67,13 21.33,14.67 22,16.67V20H10V16.67C10.67,14.67 13.33,13 16,13M8,12A4,4 0 0,1 4,8A4,4 0 0,1 8,4A4,4 0 0,1 12,8A4,4 0 0,1 8,12M8,5.5A2.5,2.5 0 0,0 5.5,8A2.5,2.5 0 0,0 8,10.5A2.5,2.5 0 0,0 10.5,8A2.5,2.5 0 0,0 8,5.5M8,13C10.67,13 13.33,14.67 14,16.67V20H2V16.67C2.67,14.67 5.33,13 8,13Z\"/>\r\n                </svg>\r\n                I am a:\r\n              </label>\r\n              <div className=\"role-options\">\r\n                <motion.div\r\n                  className={`role-option ${formData.role === 'patient' ? 'active' : ''}`}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => setFormData({...formData, role: 'patient'})}\r\n                >\r\n                  <div className=\"role-icon\">\r\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"role-content\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"patient\"\r\n                      name=\"role\"\r\n                      value=\"patient\"\r\n                      checked={formData.role === 'patient'}\r\n                      onChange={handleChange}\r\n                    />\r\n                    <label htmlFor=\"patient\">Patient</label>\r\n                    <span className=\"role-description\">Seeking medical care</span>\r\n                  </div>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className={`role-option ${formData.role === 'doctor' ? 'active' : ''}`}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => setFormData({...formData, role: 'doctor'})}\r\n                >\r\n                  <div className=\"role-icon\">\r\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"role-content\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"doctor\"\r\n                      name=\"role\"\r\n                      value=\"doctor\"\r\n                      checked={formData.role === 'doctor'}\r\n                      onChange={handleChange}\r\n                    />\r\n                    <label htmlFor=\"doctor\">Doctor</label>\r\n                    <span className=\"role-description\">Healthcare provider</span>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n            \r\n            <motion.button\r\n              type=\"submit\"\r\n              className={`auth-button ${isLoading ? 'loading' : ''}`}\r\n              whileHover={{ scale: isLoading ? 1 : 1.03 }}\r\n              whileTap={{ scale: isLoading ? 1 : 0.98 }}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? (\r\n                <div className=\"button-content\">\r\n                  <div className=\"spinner\"></div>\r\n                  Creating Account...\r\n                </div>\r\n              ) : (\r\n                <div className=\"button-content\">\r\n                  <svg className=\"button-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n                  </svg>\r\n                  Create Account\r\n                </div>\r\n              )}\r\n            </motion.button>\r\n          </form>\r\n          \r\n          <p className=\"auth-redirect\">\r\n            Already have an account? <a href=\"/login\">Sign in</a>\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Register;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC;IACvCQ,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEd,IAAI;MAAEe;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAGe;IACV,CAAC,CAAC;;IAEF;IACA,IAAIf,IAAI,KAAK,UAAU,EAAE;MACvBiB,yBAAyB,CAACF,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAME,yBAAyB,GAAIf,QAAQ,IAAK;IAC9C,IAAIgB,QAAQ,GAAG,CAAC;IAChB,IAAIhB,QAAQ,CAACiB,MAAM,IAAI,CAAC,EAAED,QAAQ,IAAI,CAAC;IACvC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,cAAc,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IAChDN,mBAAmB,CAACM,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBZ,YAAY,CAAC,IAAI,CAAC;IAClB;IACAa,UAAU,CAAC,MAAM;MACfb,YAAY,CAAC,KAAK,CAAC;MACnBc,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE3B,QAAQ,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEH,OAAA;IAAK+B,SAAS,EAAC,gBAAgB;IAACC,KAAK,EAAE;MAACC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eACvDlC,OAAA;MAAK+B,SAAS,EAAC,yBAAyB;MAAAG,QAAA,gBACtClC,OAAA,CAACF,MAAM,CAACqC,GAAG;QACTJ,SAAS,EAAC,WAAW;QACrBK,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAP,QAAA,gBAE9BlC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAG,QAAA,gBACxBlC,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAG,QAAA,eACxBlC,OAAA;cAAK0C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAT,QAAA,eAC1ClC,OAAA;gBAAM4C,CAAC,EAAC;cAAoN;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3N;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhD,OAAA;YAAAkC,QAAA,EAAI;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNhD,OAAA;UAAK+B,SAAS,EAAC,mBAAmB;UAAAG,QAAA,eAChClC,OAAA;YAAK+B,SAAS,EAAC,eAAe;YAAAG,QAAA,gBAC5BlC,OAAA;cAAK+B,SAAS,EAAC,sBAAsB;cAAAG,QAAA,eACnClC,OAAA;gBAAK0C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAC1ClC,OAAA;kBAAM4C,CAAC,EAAC;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhD,OAAA;cAAK+B,SAAS,EAAC,sBAAsB;cAAAG,QAAA,eACnClC,OAAA;gBAAK0C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAC1ClC,OAAA;kBAAM4C,CAAC,EAAC;gBAA4L;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhD,OAAA;cAAK+B,SAAS,EAAC,sBAAsB;cAAAG,QAAA,eACnClC,OAAA;gBAAK0C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAC1ClC,OAAA;kBAAM4C,CAAC,EAAC;gBAAmK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1K;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAG+B,SAAS,EAAC,cAAc;UAAAG,QAAA,EAAC;QAA6B;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAEbhD,OAAA,CAACF,MAAM,CAACqC,GAAG;QACTJ,SAAS,EAAC,qBAAqB;QAC/BK,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,KAAK,EAAE;QAAI,CAAE;QAAAf,QAAA,gBAE1ClC,OAAA;UAAAkC,QAAA,EAAI;QAAc;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBhD,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAG,QAAA,EAAC;QAAsB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEvDhD,OAAA;UAAMkD,QAAQ,EAAExB,YAAa;UAACK,SAAS,EAAC,WAAW;UAAAG,QAAA,gBACjDlC,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBlC,OAAA;cAAOmD,OAAO,EAAC,MAAM;cAAAjB,QAAA,gBACnBlC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACjElC,OAAA;kBAAM4C,CAAC,EAAC;gBAAgI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvI,CAAC,aAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAG,QAAA,eAC5BlC,OAAA;gBACEoD,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,MAAM;gBACThD,IAAI,EAAC,MAAM;gBACXe,KAAK,EAAEjB,QAAQ,CAACE,IAAK;gBACrBiD,QAAQ,EAAEpC,YAAa;gBACvBqC,WAAW,EAAC,sBAAsB;gBAClCC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBlC,OAAA;cAAOmD,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACpBlC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACjElC,OAAA;kBAAM4C,CAAC,EAAC;gBAAmH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H,CAAC,iBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAG,QAAA,eAC5BlC,OAAA;gBACEoD,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACVhD,IAAI,EAAC,OAAO;gBACZe,KAAK,EAAEjB,QAAQ,CAACG,KAAM;gBACtBgD,QAAQ,EAAEpC,YAAa;gBACvBqC,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBlC,OAAA;cAAOmD,OAAO,EAAC,UAAU;cAAAjB,QAAA,gBACvBlC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACjElC,OAAA;kBAAM4C,CAAC,EAAC;gBAA6O;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpP,CAAC,YAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAG,QAAA,gBAC5BlC,OAAA;gBACEoD,IAAI,EAAE1C,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC2C,EAAE,EAAC,UAAU;gBACbhD,IAAI,EAAC,UAAU;gBACfe,KAAK,EAAEjB,QAAQ,CAACI,QAAS;gBACzB+C,QAAQ,EAAEpC,YAAa;gBACvBqC,WAAW,EAAC,0BAA0B;gBACtCC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFhD,OAAA;gBACEoD,IAAI,EAAC,QAAQ;gBACbrB,SAAS,EAAC,iBAAiB;gBAC3B0B,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAwB,QAAA,eAE9ClC,OAAA;kBAAK0C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAT,QAAA,EACzCxB,YAAY,gBACXV,OAAA;oBAAM4C,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBhD,OAAA;oBAAM4C,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL7C,QAAQ,CAACI,QAAQ,iBAChBP,OAAA;cAAK+B,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBAChClC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAG,QAAA,eAC3BlC,OAAA;kBACE+B,SAAS,EAAE,0BAA0Bf,gBAAgB,EAAG;kBACxDgB,KAAK,EAAE;oBAAE0B,KAAK,EAAE,GAAI1C,gBAAgB,GAAG,CAAC,GAAI,GAAG;kBAAI;gBAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhD,OAAA;gBAAM+B,SAAS,EAAE,0BAA0Bf,gBAAgB,EAAG;gBAAAkB,QAAA,GAC3DlB,gBAAgB,KAAK,CAAC,IAAI,WAAW,EACrCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,QAAQ,EAClCA,gBAAgB,KAAK,CAAC,IAAI,aAAa;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhD,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAG,QAAA,gBACzBlC,OAAA;cAAOmD,OAAO,EAAC,iBAAiB;cAAAjB,QAAA,gBAC9BlC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACjElC,OAAA;kBAAM4C,CAAC,EAAC;gBAA6O;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpP,CAAC,oBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAG,QAAA,gBAC5BlC,OAAA;gBACEoD,IAAI,EAAExC,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChDyC,EAAE,EAAC,iBAAiB;gBACpBhD,IAAI,EAAC,iBAAiB;gBACtBe,KAAK,EAAEjB,QAAQ,CAACK,eAAgB;gBAChC8C,QAAQ,EAAEpC,YAAa;gBACvBqC,WAAW,EAAC,uBAAuB;gBACnCC,QAAQ;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFhD,OAAA;gBACEoD,IAAI,EAAC,QAAQ;gBACbrB,SAAS,EAAC,iBAAiB;gBAC3B0B,OAAO,EAAEA,CAAA,KAAM5C,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAAAsB,QAAA,eAE5DlC,OAAA;kBAAK0C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAT,QAAA,EACzCtB,mBAAmB,gBAClBZ,OAAA;oBAAM4C,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBhD,OAAA;oBAAM4C,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL7C,QAAQ,CAACK,eAAe,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,iBACzER,OAAA;cAAK+B,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBAChClC,OAAA;gBAAK0C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAC1ClC,OAAA;kBAAM4C,CAAC,EAAC;gBAA2G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,0BAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhD,OAAA;YAAK+B,SAAS,EAAC,2BAA2B;YAAAG,QAAA,gBACxClC,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACjElC,OAAA;kBAAM4C,CAAC,EAAC;gBAAygB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChhB,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhD,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAG,QAAA,gBAC3BlC,OAAA,CAACF,MAAM,CAACqC,GAAG;gBACTJ,SAAS,EAAE,eAAe5B,QAAQ,CAACM,IAAI,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACxEkD,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BH,OAAO,EAAEA,CAAA,KAAMrD,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEM,IAAI,EAAE;gBAAS,CAAC,CAAE;gBAAAyB,QAAA,gBAE3DlC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAG,QAAA,eACxBlC,OAAA;oBAAK0C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,eAC1ClC,OAAA;sBAAM4C,CAAC,EAAC;oBAAgI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhD,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAG,QAAA,gBAC3BlC,OAAA;oBACEoD,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,SAAS;oBACZhD,IAAI,EAAC,MAAM;oBACXe,KAAK,EAAC,SAAS;oBACf0C,OAAO,EAAE3D,QAAQ,CAACM,IAAI,KAAK,SAAU;oBACrC6C,QAAQ,EAAEpC;kBAAa;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFhD,OAAA;oBAAOmD,OAAO,EAAC,SAAS;oBAAAjB,QAAA,EAAC;kBAAO;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxChD,OAAA;oBAAM+B,SAAS,EAAC,kBAAkB;oBAAAG,QAAA,EAAC;kBAAoB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbhD,OAAA,CAACF,MAAM,CAACqC,GAAG;gBACTJ,SAAS,EAAE,eAAe5B,QAAQ,CAACM,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACvEkD,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BH,OAAO,EAAEA,CAAA,KAAMrD,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEM,IAAI,EAAE;gBAAQ,CAAC,CAAE;gBAAAyB,QAAA,gBAE1DlC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAG,QAAA,eACxBlC,OAAA;oBAAK0C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAT,QAAA,eAC1ClC,OAAA;sBAAM4C,CAAC,EAAC;oBAA4L;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhD,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAG,QAAA,gBAC3BlC,OAAA;oBACEoD,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,QAAQ;oBACXhD,IAAI,EAAC,MAAM;oBACXe,KAAK,EAAC,QAAQ;oBACd0C,OAAO,EAAE3D,QAAQ,CAACM,IAAI,KAAK,QAAS;oBACpC6C,QAAQ,EAAEpC;kBAAa;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFhD,OAAA;oBAAOmD,OAAO,EAAC,QAAQ;oBAAAjB,QAAA,EAAC;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtChD,OAAA;oBAAM+B,SAAS,EAAC,kBAAkB;oBAAAG,QAAA,EAAC;kBAAmB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA,CAACF,MAAM,CAACiE,MAAM;YACZX,IAAI,EAAC,QAAQ;YACbrB,SAAS,EAAE,eAAejB,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YACvD6C,UAAU,EAAE;cAAEC,KAAK,EAAE9C,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC5C+C,QAAQ,EAAE;cAAED,KAAK,EAAE9C,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC1CkD,QAAQ,EAAElD,SAAU;YAAAoB,QAAA,EAEnBpB,SAAS,gBACRd,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAG,QAAA,gBAC7BlC,OAAA;gBAAK+B,SAAS,EAAC;cAAS;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,uBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAENhD,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAG,QAAA,gBAC7BlC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAACW,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAClElC,OAAA;kBAAM4C,CAAC,EAAC;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,kBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEPhD,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAG,QAAA,GAAC,2BACF,eAAAlC,OAAA;YAAGiE,IAAI,EAAC,QAAQ;YAAA/B,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA7TID,QAAQ;AAAAiE,EAAA,GAARjE,QAAQ;AA+Td,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}