{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\components\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport '../../styles/fonts.css';\nimport '../../styles/colors.css';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    // Simulate loading\n    setTimeout(() => {\n      setIsLoading(false);\n      console.log('Login attempt:', {\n        email,\n        password\n      });\n    }, 1500);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card glass-strong\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-left bg-gradient-ocean\",\n        initial: {\n          opacity: 0,\n          x: -50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-logo\",\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon glass\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"logo-text text-inverse\",\n            children: \"Doctorly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-illustration\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"medical-scene\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-elements\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-1\",\n                animate: {\n                  y: [0, -10, 0],\n                  rotate: [0, 2, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-2\",\n                animate: {\n                  y: [0, -15, 0],\n                  rotate: [0, -3, 0]\n                },\n                transition: {\n                  duration: 5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-3\",\n                animate: {\n                  y: [0, -8, 0],\n                  rotate: [0, 1, 0]\n                },\n                transition: {\n                  duration: 6,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-rings\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-tagline body-large text-inverse text-shadow-md\",\n            children: \"Your health, our priority\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tagline-accent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-form-container\",\n        initial: {\n          opacity: 0,\n          x: 50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Sign in to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,15C12.81,15 13.5,14.7 14.11,14.11C14.7,13.5 15,12.81 15,12C15,11.19 14.7,10.5 14.11,9.89C13.5,9.3 12.81,9 12,9C11.19,9 10.5,9.3 9.89,9.89C9.3,10.5 9,11.19 9,12C9,12.81 9.3,13.5 9.89,14.11C10.5,14.7 11.19,15 12,15M12,2C14.21,2 16.21,2.81 17.78,4.39C19.36,5.96 20.17,7.96 20.17,10.17C20.17,12.54 19.5,14.69 18.17,16.5C16.84,18.31 15.33,19.83 13.62,21.06C12.81,21.72 11.19,21.72 10.38,21.06C8.67,19.83 7.16,18.31 5.83,16.5C4.5,14.69 3.83,12.54 3.83,10.17C3.83,7.96 4.64,5.96 6.22,4.39C7.79,2.81 9.79,2 12,2Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), \"Email Address\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                value: email,\n                onChange: e => setEmail(e.target.value),\n                placeholder: \"Enter your email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), \"Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                value: password,\n                onChange: e => setPassword(e.target.value),\n                placeholder: \"Enter your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowPassword(!showPassword),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-options\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"remember-me\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"remember\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/forgot-password\",\n              className: \"forgot-password\",\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: `auth-button ${isLoading ? 'loading' : ''}`,\n            whileHover: {\n              scale: isLoading ? 1 : 1.03\n            },\n            whileTap: {\n              scale: isLoading ? 1 : 0.98\n            },\n            disabled: isLoading,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), \"Signing In...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"button-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10,17V14H3V10H10V7L15,12L10,17Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), \"Sign In\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-redirect\",\n          children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/register\",\n            children: \"Sign up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 36\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"n/918ykgqwH1kfZw/yuU9K8WPlA=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "motion", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "isLoading", "setIsLoading", "handleSubmit", "e", "preventDefault", "setTimeout", "console", "log", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "ease", "y", "delay", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "rotate", "repeat", "Infinity", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "onClick", "href", "button", "whileHover", "whileTap", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport '../../styles/fonts.css';\r\nimport '../../styles/colors.css';\r\nimport '../../styles/auth.css';\r\n\r\nconst Login = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    // Simulate loading\r\n    setTimeout(() => {\r\n      setIsLoading(false);\r\n      console.log('Login attempt:', { email, password });\r\n    }, 1500);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card glass-strong\">\r\n        <motion.div\r\n          className=\"auth-left bg-gradient-ocean\"\r\n          initial={{ opacity: 0, x: -50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n        >\r\n          <motion.div\r\n            className=\"auth-logo\"\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n          >\r\n            <div className=\"logo-icon glass\">\r\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n              </svg>\r\n            </div>\r\n            <h2 className=\"logo-text text-inverse\">Doctorly</h2>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"auth-illustration\"\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n          >\r\n            <div className=\"medical-scene\">\r\n              <div className=\"floating-elements\">\r\n                <motion.div\r\n                  className=\"medical-card card-1\"\r\n                  animate={{\r\n                    y: [0, -10, 0],\r\n                    rotate: [0, 2, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 4,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-2\"\r\n                  animate={{\r\n                    y: [0, -15, 0],\r\n                    rotate: [0, -3, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 5,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 1\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-3\"\r\n                  animate={{\r\n                    y: [0, -8, 0],\r\n                    rotate: [0, 1, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 6,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 2\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n              </div>\r\n\r\n              <div className=\"pulse-rings\">\r\n                <div className=\"pulse-ring ring-1\"></div>\r\n                <div className=\"pulse-ring ring-2\"></div>\r\n                <div className=\"pulse-ring ring-3\"></div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.6 }}\r\n          >\r\n            <p className=\"auth-tagline body-large text-inverse text-shadow-md\">\r\n              Your health, our priority\r\n            </p>\r\n            <div className=\"tagline-accent\"></div>\r\n          </motion.div>\r\n        </motion.div>\r\n        \r\n        <motion.div \r\n          className=\"auth-form-container\"\r\n          initial={{ opacity: 0, x: 50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n        >\r\n          <h1>Welcome Back</h1>\r\n          <p className=\"auth-subtitle\">Sign in to continue</p>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"auth-form\">\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"email\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,15C12.81,15 13.5,14.7 14.11,14.11C14.7,13.5 15,12.81 15,12C15,11.19 14.7,10.5 14.11,9.89C13.5,9.3 12.81,9 12,9C11.19,9 10.5,9.3 9.89,9.89C9.3,10.5 9,11.19 9,12C9,12.81 9.3,13.5 9.89,14.11C10.5,14.7 11.19,15 12,15M12,2C14.21,2 16.21,2.81 17.78,4.39C19.36,5.96 20.17,7.96 20.17,10.17C20.17,12.54 19.5,14.69 18.17,16.5C16.84,18.31 15.33,19.83 13.62,21.06C12.81,21.72 11.19,21.72 10.38,21.06C8.67,19.83 7.16,18.31 5.83,16.5C4.5,14.69 3.83,12.54 3.83,10.17C3.83,7.96 4.64,5.96 6.22,4.39C7.79,2.81 9.79,2 12,2Z\"/>\r\n                </svg>\r\n                Email Address\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  placeholder=\"Enter your email\"\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"password\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                </svg>\r\n                Password\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"password\"\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                  placeholder=\"Enter your password\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"password-toggle\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"form-options\">\r\n              <div className=\"remember-me\">\r\n                <input type=\"checkbox\" id=\"remember\" />\r\n                <label htmlFor=\"remember\">Remember me</label>\r\n              </div>\r\n              <a href=\"/forgot-password\" className=\"forgot-password\">Forgot password?</a>\r\n            </div>\r\n            \r\n            <motion.button\r\n              type=\"submit\"\r\n              className={`auth-button ${isLoading ? 'loading' : ''}`}\r\n              whileHover={{ scale: isLoading ? 1 : 1.03 }}\r\n              whileTap={{ scale: isLoading ? 1 : 0.98 }}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? (\r\n                <div className=\"button-content\">\r\n                  <div className=\"spinner\"></div>\r\n                  Signing In...\r\n                </div>\r\n              ) : (\r\n                <div className=\"button-content\">\r\n                  <svg className=\"button-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M10,17V14H3V10H10V7L15,12L10,17Z\"/>\r\n                  </svg>\r\n                  Sign In\r\n                </div>\r\n              )}\r\n            </motion.button>\r\n          </form>\r\n          \r\n          <p className=\"auth-redirect\">\r\n            Don't have an account? <a href=\"/register\">Sign up</a>\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,wBAAwB;AAC/B,OAAO,yBAAyB;AAChC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMc,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,YAAY,CAAC,IAAI,CAAC;IAClB;IACAI,UAAU,CAAC,MAAM;MACfJ,YAAY,CAAC,KAAK,CAAC;MACnBK,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAEb,KAAK;QAAEE;MAAS,CAAC,CAAC;IACpD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEL,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BlB,OAAA;MAAKiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrClB,OAAA,CAACF,MAAM,CAACqB,GAAG;QACTF,SAAS,EAAC,6BAA6B;QACvCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAAAR,QAAA,gBAE/ClB,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTF,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAKiB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BlB,OAAA;cAAK6B,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAZ,QAAA,eAC1ClB,OAAA;gBAAM+B,CAAC,EAAC;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAIiB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTF,SAAS,EAAC,mBAAmB;UAC7BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAI,CAAE;UACpCb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAE,CAAE;UAClCZ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,eAE1ClB,OAAA;YAAKiB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlB,OAAA;cAAKiB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClB,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE;gBACR,CAAE;gBAAAR,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAA4L;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAAgI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACbU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAAmK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnC,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlB,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCnC,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCnC,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAGiB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEnE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnC,OAAA;YAAKiB,SAAS,EAAC;UAAgB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;QACTF,SAAS,EAAC,qBAAqB;QAC/BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAAAV,QAAA,gBAE1ClB,OAAA;UAAAkB,QAAA,EAAI;QAAY;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBnC,OAAA;UAAGiB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEpDnC,OAAA;UAAMwC,QAAQ,EAAE7B,YAAa;UAACM,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjDlB,OAAA;YAAKiB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlB,OAAA;cAAOyC,OAAO,EAAC,OAAO;cAAAvB,QAAA,gBACpBlB,OAAA;gBAAKiB,SAAS,EAAC,YAAY;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eACjElB,OAAA;kBAAM+B,CAAC,EAAC;gBAA8f;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrgB,CAAC,iBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BlB,OAAA;gBACE0C,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEzC,KAAM;gBACb0C,QAAQ,EAAGjC,CAAC,IAAKR,QAAQ,CAACQ,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAC1CG,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnC,OAAA;YAAKiB,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlB,OAAA;cAAOyC,OAAO,EAAC,UAAU;cAAAvB,QAAA,gBACvBlB,OAAA;gBAAKiB,SAAS,EAAC,YAAY;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eACjElB,OAAA;kBAAM+B,CAAC,EAAC;gBAA6O;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpP,CAAC,YAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnC,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BlB,OAAA;gBACE0C,IAAI,EAAEnC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCoC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEvC,QAAS;gBAChBwC,QAAQ,EAAGjC,CAAC,IAAKN,WAAW,CAACM,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAC7CG,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFnC,OAAA;gBACE0C,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,iBAAiB;gBAC3BgC,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAW,QAAA,eAE9ClB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,EACzCX,YAAY,gBACXP,OAAA;oBAAM+B,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBnC,OAAA;oBAAM+B,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnC,OAAA;YAAKiB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlB,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlB,OAAA;gBAAO0C,IAAI,EAAC,UAAU;gBAACC,EAAE,EAAC;cAAU;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnC,OAAA;gBAAOyC,OAAO,EAAC,UAAU;gBAAAvB,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNnC,OAAA;cAAGkD,IAAI,EAAC,kBAAkB;cAACjC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAgB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAENnC,OAAA,CAACF,MAAM,CAACqD,MAAM;YACZT,IAAI,EAAC,QAAQ;YACbzB,SAAS,EAAE,eAAeR,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YACvD2C,UAAU,EAAE;cAAEhB,KAAK,EAAE3B,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC5C4C,QAAQ,EAAE;cAAEjB,KAAK,EAAE3B,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC1C6C,QAAQ,EAAE7C,SAAU;YAAAS,QAAA,EAEnBT,SAAS,gBACRT,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA;gBAAKiB,SAAS,EAAC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAENnC,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eAClElB,OAAA;kBAAM+B,CAAC,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEPnC,OAAA;UAAGiB,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,yBACJ,eAAAlB,OAAA;YAAGkD,IAAI,EAAC,WAAW;YAAAhC,QAAA,EAAC;UAAO;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA5NID,KAAK;AAAAsD,EAAA,GAALtD,KAAK;AA8NX,eAAeA,KAAK;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}