{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\components\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport '../../styles/fonts.css';\nimport '../../styles/colors.css';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    // Simulate loading\n    setTimeout(() => {\n      setIsLoading(false);\n      console.log('Login attempt:', {\n        email,\n        password\n      });\n    }, 1500);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card glass-strong\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-left bg-gradient-ocean\",\n        initial: {\n          opacity: 0,\n          x: -50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-logo\",\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon glass\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"logo-text text-inverse\",\n            children: \"Doctorly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-illustration\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"medical-scene\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-elements\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-1\",\n                animate: {\n                  y: [0, -10, 0],\n                  rotate: [0, 2, 0]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-2\",\n                animate: {\n                  y: [0, -15, 0],\n                  rotate: [0, -3, 0]\n                },\n                transition: {\n                  duration: 5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-3\",\n                animate: {\n                  y: [0, -8, 0],\n                  rotate: [0, 1, 0]\n                },\n                transition: {\n                  duration: 6,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-rings\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-tagline body-large text-inverse text-shadow-md\",\n            children: \"Your health, our priority\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tagline-accent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-form-container\",\n        initial: {\n          opacity: 0,\n          x: 50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.3,\n          ease: \"easeOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"heading-1 text-primary\",\n            children: \"Welcome Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-subtitle body-large text-secondary\",\n            children: \"Sign in to continue your healthcare journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.form, {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.7\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"floating-input-group\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.8\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"input-icon\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                value: email,\n                onChange: e => setEmail(e.target.value),\n                className: \"floating-input focus-primary color-transition\",\n                placeholder: \" \",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"floating-label label text-secondary\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"floating-input-group\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.9\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"input-icon\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                value: password,\n                onChange: e => setPassword(e.target.value),\n                className: \"floating-input focus-primary color-transition\",\n                placeholder: \" \",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"floating-label label text-secondary\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                className: \"password-toggle-btn\",\n                onClick: () => setShowPassword(!showPassword),\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"form-options\",\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 1.0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"custom-checkbox\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"remember\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"checkmark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"checkbox-label body-small text-secondary\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n              href: \"/forgot-password\",\n              className: \"forgot-password body-small text-accent\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Forgot password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: `modern-auth-button ${isLoading ? 'loading' : ''}`,\n            whileHover: {\n              scale: isLoading ? 1 : 1.02,\n              boxShadow: isLoading ? \"0 8px 25px rgba(20, 184, 166, 0.25)\" : \"0 12px 35px rgba(20, 184, 166, 0.35)\"\n            },\n            whileTap: {\n              scale: isLoading ? 1 : 0.98\n            },\n            disabled: isLoading,\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 1.1\n            },\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"modern-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-text\",\n                children: \"Signing In...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"button-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10,17V14H3V10H10V7L15,12L10,17Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-text\",\n                children: \"Sign In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-footer\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 1.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-redirect body-regular text-secondary\",\n            children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(motion.a, {\n              href: \"/register\",\n              className: \"auth-link text-accent font-semibold\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Sign up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"social-login\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divider\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"divider-text caption text-muted\",\n                children: \"Or continue with\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                className: \"social-btn google-btn\",\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                    fill: \"#4285F4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                    fill: \"#34A853\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                    fill: \"#FBBC05\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                    fill: \"#EA4335\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                className: \"social-btn apple-btn\",\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"n/918ykgqwH1kfZw/yuU9K8WPlA=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "motion", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "isLoading", "setIsLoading", "handleSubmit", "e", "preventDefault", "setTimeout", "console", "log", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "ease", "y", "delay", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "rotate", "repeat", "Infinity", "form", "onSubmit", "type", "id", "value", "onChange", "target", "placeholder", "required", "htmlFor", "button", "onClick", "whileHover", "whileTap", "a", "href", "boxShadow", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport '../../styles/fonts.css';\r\nimport '../../styles/colors.css';\r\nimport '../../styles/auth.css';\r\n\r\nconst Login = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    // Simulate loading\r\n    setTimeout(() => {\r\n      setIsLoading(false);\r\n      console.log('Login attempt:', { email, password });\r\n    }, 1500);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card glass-strong\">\r\n        <motion.div\r\n          className=\"auth-left bg-gradient-ocean\"\r\n          initial={{ opacity: 0, x: -50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n        >\r\n          <motion.div\r\n            className=\"auth-logo\"\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n          >\r\n            <div className=\"logo-icon glass\">\r\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n              </svg>\r\n            </div>\r\n            <h2 className=\"logo-text text-inverse\">Doctorly</h2>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"auth-illustration\"\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n          >\r\n            <div className=\"medical-scene\">\r\n              <div className=\"floating-elements\">\r\n                <motion.div\r\n                  className=\"medical-card card-1\"\r\n                  animate={{\r\n                    y: [0, -10, 0],\r\n                    rotate: [0, 2, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 4,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-2\"\r\n                  animate={{\r\n                    y: [0, -15, 0],\r\n                    rotate: [0, -3, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 5,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 1\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-3\"\r\n                  animate={{\r\n                    y: [0, -8, 0],\r\n                    rotate: [0, 1, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 6,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 2\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n              </div>\r\n\r\n              <div className=\"pulse-rings\">\r\n                <div className=\"pulse-ring ring-1\"></div>\r\n                <div className=\"pulse-ring ring-2\"></div>\r\n                <div className=\"pulse-ring ring-3\"></div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.6 }}\r\n          >\r\n            <p className=\"auth-tagline body-large text-inverse text-shadow-md\">\r\n              Your health, our priority\r\n            </p>\r\n            <div className=\"tagline-accent\"></div>\r\n          </motion.div>\r\n        </motion.div>\r\n        \r\n        <motion.div\r\n          className=\"auth-form-container\"\r\n          initial={{ opacity: 0, x: 50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.3, ease: \"easeOut\" }}\r\n        >\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.5 }}\r\n          >\r\n            <h1 className=\"heading-1 text-primary\">Welcome Back</h1>\r\n            <p className=\"auth-subtitle body-large text-secondary\">\r\n              Sign in to continue your healthcare journey\r\n            </p>\r\n          </motion.div>\r\n          \r\n          <motion.form\r\n            onSubmit={handleSubmit}\r\n            className=\"auth-form\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.7 }}\r\n          >\r\n            <motion.div\r\n              className=\"floating-input-group\"\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.8 }}\r\n            >\r\n              <div className=\"input-container\">\r\n                <div className=\"input-icon-wrapper\">\r\n                  <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  className=\"floating-input focus-primary color-transition\"\r\n                  placeholder=\" \"\r\n                  required\r\n                />\r\n                <label htmlFor=\"email\" className=\"floating-label label text-secondary\">\r\n                  Email Address\r\n                </label>\r\n                <div className=\"input-border\"></div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              className=\"floating-input-group\"\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.9 }}\r\n            >\r\n              <div className=\"input-container\">\r\n                <div className=\"input-icon-wrapper\">\r\n                  <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"password\"\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                  className=\"floating-input focus-primary color-transition\"\r\n                  placeholder=\" \"\r\n                  required\r\n                />\r\n                <label htmlFor=\"password\" className=\"floating-label label text-secondary\">\r\n                  Password\r\n                </label>\r\n                <motion.button\r\n                  type=\"button\"\r\n                  className=\"password-toggle-btn\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </motion.button>\r\n                <div className=\"input-border\"></div>\r\n              </div>\r\n            </motion.div>\r\n            \r\n            <motion.div\r\n              className=\"form-options\"\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 1.0 }}\r\n            >\r\n              <label className=\"custom-checkbox\">\r\n                <input type=\"checkbox\" id=\"remember\" />\r\n                <span className=\"checkmark\"></span>\r\n                <span className=\"checkbox-label body-small text-secondary\">Remember me</span>\r\n              </label>\r\n              <motion.a\r\n                href=\"/forgot-password\"\r\n                className=\"forgot-password body-small text-accent\"\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                Forgot password?\r\n              </motion.a>\r\n            </motion.div>\r\n\r\n            <motion.button\r\n              type=\"submit\"\r\n              className={`modern-auth-button ${isLoading ? 'loading' : ''}`}\r\n              whileHover={{\r\n                scale: isLoading ? 1 : 1.02,\r\n                boxShadow: isLoading ? \"0 8px 25px rgba(20, 184, 166, 0.25)\" : \"0 12px 35px rgba(20, 184, 166, 0.35)\"\r\n              }}\r\n              whileTap={{ scale: isLoading ? 1 : 0.98 }}\r\n              disabled={isLoading}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 1.1 }}\r\n            >\r\n              {isLoading ? (\r\n                <div className=\"button-content\">\r\n                  <div className=\"modern-spinner\"></div>\r\n                  <span className=\"button-text\">Signing In...</span>\r\n                </div>\r\n              ) : (\r\n                <div className=\"button-content\">\r\n                  <svg className=\"button-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M10,17V14H3V10H10V7L15,12L10,17Z\"/>\r\n                  </svg>\r\n                  <span className=\"button-text\">Sign In</span>\r\n                </div>\r\n              )}\r\n            </motion.button>\r\n          </motion.form>\r\n          \r\n          <motion.div\r\n            className=\"auth-footer\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 1.2 }}\r\n          >\r\n            <p className=\"auth-redirect body-regular text-secondary\">\r\n              Don't have an account?{' '}\r\n              <motion.a\r\n                href=\"/register\"\r\n                className=\"auth-link text-accent font-semibold\"\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                Sign up\r\n              </motion.a>\r\n            </p>\r\n\r\n            <div className=\"social-login\">\r\n              <div className=\"divider\">\r\n                <span className=\"divider-text caption text-muted\">Or continue with</span>\r\n              </div>\r\n              <div className=\"social-buttons\">\r\n                <motion.button\r\n                  className=\"social-btn google-btn\"\r\n                  whileHover={{ scale: 1.05, y: -2 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" fill=\"#4285F4\"/>\r\n                    <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" fill=\"#34A853\"/>\r\n                    <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\" fill=\"#FBBC05\"/>\r\n                    <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" fill=\"#EA4335\"/>\r\n                  </svg>\r\n                </motion.button>\r\n                <motion.button\r\n                  className=\"social-btn apple-btn\"\r\n                  whileHover={{ scale: 1.05, y: -2 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\r\n                  </svg>\r\n                </motion.button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,wBAAwB;AAC/B,OAAO,yBAAyB;AAChC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMc,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,YAAY,CAAC,IAAI,CAAC;IAClB;IACAI,UAAU,CAAC,MAAM;MACfJ,YAAY,CAAC,KAAK,CAAC;MACnBK,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAEb,KAAK;QAAEE;MAAS,CAAC,CAAC;IACpD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEL,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BlB,OAAA;MAAKiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrClB,OAAA,CAACF,MAAM,CAACqB,GAAG;QACTF,SAAS,EAAC,6BAA6B;QACvCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAAAR,QAAA,gBAE/ClB,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTF,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAKiB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BlB,OAAA;cAAK6B,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAZ,QAAA,eAC1ClB,OAAA;gBAAM+B,CAAC,EAAC;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAIiB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTF,SAAS,EAAC,mBAAmB;UAC7BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAI,CAAE;UACpCb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAE,CAAE;UAClCZ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,eAE1ClB,OAAA;YAAKiB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlB,OAAA;cAAKiB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClB,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE;gBACR,CAAE;gBAAAR,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAA4L;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAAgI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACbU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAAmK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnC,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlB,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCnC,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCnC,OAAA;gBAAKiB,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAGiB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEnE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnC,OAAA;YAAKiB,SAAS,EAAC;UAAgB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;QACTF,SAAS,EAAC,qBAAqB;QAC/BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE,GAAG;UAAEF,IAAI,EAAE;QAAU,CAAE;QAAAR,QAAA,gBAE3DlB,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAIiB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAY;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDnC,OAAA;YAAGiB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAAC0C,IAAI;UACVC,QAAQ,EAAE9B,YAAa;UACvBM,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA,CAACF,MAAM,CAACqB,GAAG;YACTF,SAAS,EAAC,sBAAsB;YAChCG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,eAE1ClB,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlB,OAAA;gBAAKiB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjClB,OAAA;kBAAKiB,SAAS,EAAC,YAAY;kBAACY,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eACjElB,OAAA;oBAAM+B,CAAC,EAAC;kBAAmH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnC,OAAA;gBACE0C,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAEzC,KAAM;gBACb0C,QAAQ,EAAGjC,CAAC,IAAKR,QAAQ,CAACQ,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAC1C3B,SAAS,EAAC,+CAA+C;gBACzD8B,WAAW,EAAC,GAAG;gBACfC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFnC,OAAA;gBAAOiD,OAAO,EAAC,OAAO;gBAAChC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEvE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA;gBAAKiB,SAAS,EAAC;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;YACTF,SAAS,EAAC,sBAAsB;YAChCG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,eAE1ClB,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlB,OAAA;gBAAKiB,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjClB,OAAA;kBAAKiB,SAAS,EAAC,YAAY;kBAACY,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eACjElB,OAAA;oBAAM+B,CAAC,EAAC;kBAA6O;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnC,OAAA;gBACE0C,IAAI,EAAEnC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCoC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAEvC,QAAS;gBAChBwC,QAAQ,EAAGjC,CAAC,IAAKN,WAAW,CAACM,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAC7C3B,SAAS,EAAC,+CAA+C;gBACzD8B,WAAW,EAAC,GAAG;gBACfC,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFnC,OAAA;gBAAOiD,OAAO,EAAC,UAAU;gBAAChC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRnC,OAAA,CAACF,MAAM,CAACoD,MAAM;gBACZR,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,qBAAqB;gBAC/BkC,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9C6C,UAAU,EAAE;kBAAEhB,KAAK,EAAE;gBAAI,CAAE;gBAC3BiB,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,eAEzBlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,EACzCX,YAAY,gBACXP,OAAA;oBAAM+B,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBnC,OAAA;oBAAM+B,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAChBnC,OAAA;gBAAKiB,SAAS,EAAC;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACqB,GAAG;YACTF,SAAS,EAAC,cAAc;YACxBG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAG,CAAE;YAC/BJ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAE,CAAE;YAC9BH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,gBAE1ClB,OAAA;cAAOiB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAChClB,OAAA;gBAAO0C,IAAI,EAAC,UAAU;gBAACC,EAAE,EAAC;cAAU;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnC,OAAA;gBAAMiB,SAAS,EAAC;cAAW;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnCnC,OAAA;gBAAMiB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACRnC,OAAA,CAACF,MAAM,CAACwD,CAAC;cACPC,IAAI,EAAC,kBAAkB;cACvBtC,SAAS,EAAC,wCAAwC;cAClDmC,UAAU,EAAE;gBAAEhB,KAAK,EAAE;cAAK,CAAE;cAC5BiB,QAAQ,EAAE;gBAAEjB,KAAK,EAAE;cAAK,CAAE;cAAAlB,QAAA,EAC3B;YAED;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEbnC,OAAA,CAACF,MAAM,CAACoD,MAAM;YACZR,IAAI,EAAC,QAAQ;YACbzB,SAAS,EAAE,sBAAsBR,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YAC9D2C,UAAU,EAAE;cACVhB,KAAK,EAAE3B,SAAS,GAAG,CAAC,GAAG,IAAI;cAC3B+C,SAAS,EAAE/C,SAAS,GAAG,qCAAqC,GAAG;YACjE,CAAE;YACF4C,QAAQ,EAAE;cAAEjB,KAAK,EAAE3B,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC1CgD,QAAQ,EAAEhD,SAAU;YACpBW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAG,CAAE;YAC/BJ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAE,CAAE;YAC9BH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,EAEzCT,SAAS,gBACRT,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA;gBAAKiB,SAAS,EAAC;cAAgB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCnC,OAAA;gBAAMiB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAa;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,gBAENnC,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eAClElB,OAAA;kBAAM+B,CAAC,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNnC,OAAA;gBAAMiB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEdnC,OAAA,CAACF,MAAM,CAACqB,GAAG;UACTF,SAAS,EAAC,aAAa;UACvBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ClB,OAAA;YAAGiB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,GAAC,wBACjC,EAAC,GAAG,eAC1BlB,OAAA,CAACF,MAAM,CAACwD,CAAC;cACPC,IAAI,EAAC,WAAW;cAChBtC,SAAS,EAAC,qCAAqC;cAC/CmC,UAAU,EAAE;gBAAEhB,KAAK,EAAE;cAAK,CAAE;cAC5BiB,QAAQ,EAAE;gBAAEjB,KAAK,EAAE;cAAK,CAAE;cAAAlB,QAAA,EAC3B;YAED;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEJnC,OAAA;YAAKiB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlB,OAAA;cAAKiB,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBlB,OAAA;gBAAMiB,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNnC,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA,CAACF,MAAM,CAACoD,MAAM;gBACZjC,SAAS,EAAC,uBAAuB;gBACjCmC,UAAU,EAAE;kBAAEhB,KAAK,EAAE,IAAI;kBAAET,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnC0B,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAK,CAAE;gBAAAlB,QAAA,eAE1BlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,gBAC1ClB,OAAA;oBAAM+B,CAAC,EAAC,yHAAyH;oBAACD,IAAI,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAClJnC,OAAA;oBAAM+B,CAAC,EAAC,uIAAuI;oBAACD,IAAI,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChKnC,OAAA;oBAAM+B,CAAC,EAAC,+HAA+H;oBAACD,IAAI,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACxJnC,OAAA;oBAAM+B,CAAC,EAAC,qIAAqI;oBAACD,IAAI,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3J;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAChBnC,OAAA,CAACF,MAAM,CAACoD,MAAM;gBACZjC,SAAS,EAAC,sBAAsB;gBAChCmC,UAAU,EAAE;kBAAEhB,KAAK,EAAE,IAAI;kBAAET,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnC0B,QAAQ,EAAE;kBAAEjB,KAAK,EAAE;gBAAK,CAAE;gBAAAlB,QAAA,eAE1BlB,OAAA;kBAAK6B,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ClB,OAAA;oBAAM+B,CAAC,EAAC;kBAA2b;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClc;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA7TID,KAAK;AAAAyD,EAAA,GAALzD,KAAK;AA+TX,eAAeA,KAAK;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}