{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\App.js\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Doctorly\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 3\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/App.js"], "sourcesContent": ["import './App.css';\n\nfunction App() {\n\treturn (\n\t\t<div className=\"App\">\n\t\t\t<h1>Doctorly</h1>\n\t\t</div>\n  \t);\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAAC,SAAAA,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACd,oBACCD,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,eACnBH,OAAA;MAAAG,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAER;AAACC,EAAA,GANQP,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}