import React, { useState } from 'react';
import { motion } from 'framer-motion';
import '../../styles/auth.css';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    // Login logic will be implemented later
    console.log('Login attempt:', { email, password });
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <motion.div 
          className="auth-left"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="auth-logo">
            <h2>Doctorly</h2>
          </div>
          <div className="auth-illustration">
            {/* Medical illustration will go here */}
          </div>
          <p className="auth-tagline">Your health, our priority</p>
        </motion.div>
        
        <motion.div 
          className="auth-form-container"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h1>Welcome Back</h1>
          <p className="auth-subtitle">Sign in to continue</p>
          
          <form onSubmit={handleSubmit} className="auth-form">
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            
            <div className="form-options">
              <div className="remember-me">
                <input type="checkbox" id="remember" />
                <label htmlFor="remember">Remember me</label>
              </div>
              <a href="/forgot-password" className="forgot-password">Forgot password?</a>
            </div>
            
            <motion.button 
              type="submit" 
              className="auth-button"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              Sign In
            </motion.button>
          </form>
          
          <p className="auth-redirect">
            Don't have an account? <a href="/register">Sign up</a>
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;