{"ast": null, "code": "var _jsxFileName = \"E:\\\\projects\\\\doctorly\\\\src\\\\components\\\\Auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport '../../styles/fonts.css';\nimport '../../styles/colors.css';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'patient'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Calculate password strength\n    if (name === 'password') {\n      calculatePasswordStrength(value);\n    }\n  };\n  const calculatePasswordStrength = password => {\n    let strength = 0;\n    if (password.length >= 8) strength += 1;\n    if (/[A-Z]/.test(password)) strength += 1;\n    if (/[a-z]/.test(password)) strength += 1;\n    if (/[0-9]/.test(password)) strength += 1;\n    if (/[^A-Za-z0-9]/.test(password)) strength += 1;\n    setPasswordStrength(strength);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsLoading(true);\n    // Simulate loading\n    setTimeout(() => {\n      setIsLoading(false);\n      console.log('Registration data:', formData);\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card register-card glass-strong\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-left bg-gradient-aurora\",\n        initial: {\n          opacity: 0,\n          x: -50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-logo\",\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon glass\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"logo-text text-inverse\",\n            children: \"Doctorly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"auth-illustration\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"registration-scene\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"floating-elements\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-1\",\n                animate: {\n                  y: [0, -12, 0],\n                  rotate: [0, 3, 0]\n                },\n                transition: {\n                  duration: 5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-2\",\n                animate: {\n                  y: [0, -18, 0],\n                  rotate: [0, -2, 0]\n                },\n                transition: {\n                  duration: 6,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 1.5\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"medical-card card-3\",\n                animate: {\n                  y: [0, -10, 0],\n                  rotate: [0, 1, 0]\n                },\n                transition: {\n                  duration: 7,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                  delay: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-rings\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pulse-ring ring-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-tagline body-large text-inverse text-shadow-md\",\n            children: \"Join us for better healthcare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tagline-accent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"auth-form-container\",\n        initial: {\n          opacity: 0,\n          x: 50\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.3,\n          ease: \"easeOut\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"heading-1 text-primary\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"auth-subtitle body-large text-secondary\",\n            children: \"Join thousands of users for better healthcare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.form, {\n          onSubmit: handleSubmit,\n          className: \"auth-form register-form\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.7\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"floating-input-group\",\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.8\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-icon-wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"input-icon\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: \"floating-input focus-primary color-transition\",\n                  placeholder: \" \",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"name\",\n                  className: \"floating-label label text-secondary\",\n                  children: \"Full Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-border\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"floating-input-group\",\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.9\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-icon-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"input-icon\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                className: \"floating-input focus-primary color-transition\",\n                placeholder: \" \",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"floating-label label text-secondary\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), \"Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Create a strong password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowPassword(!showPassword),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"password-strength\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"strength-bar\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `strength-fill strength-${passwordStrength}`,\n                  style: {\n                    width: `${passwordStrength / 5 * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `strength-text strength-${passwordStrength}`,\n                children: [passwordStrength === 0 && 'Very Weak', passwordStrength === 1 && 'Weak', passwordStrength === 2 && 'Fair', passwordStrength === 3 && 'Good', passwordStrength === 4 && 'Strong', passwordStrength === 5 && 'Very Strong']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), \"Confirm Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showConfirmPassword ? \"text\" : \"password\",\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                placeholder: \"Confirm your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"password-toggle\",\n                onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  viewBox: \"0 0 24 24\",\n                  fill: \"currentColor\",\n                  children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), formData.confirmPassword && formData.password !== formData.confirmPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"password-mismatch\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), \"Passwords do not match\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group role-selection\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"input-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16,4C16.88,4 17.67,4.38 18.18,5L20.5,2.69L21.81,4L19.5,6.31C19.81,6.81 20,7.39 20,8A4,4 0 0,1 16,12A4,4 0 0,1 12,8A4,4 0 0,1 16,4M16,5.5A2.5,2.5 0 0,0 13.5,8A2.5,2.5 0 0,0 16,10.5A2.5,2.5 0 0,0 18.5,8A2.5,2.5 0 0,0 16,5.5M16,13C18.67,13 21.33,14.67 22,16.67V20H10V16.67C10.67,14.67 13.33,13 16,13M8,12A4,4 0 0,1 4,8A4,4 0 0,1 8,4A4,4 0 0,1 12,8A4,4 0 0,1 8,12M8,5.5A2.5,2.5 0 0,0 5.5,8A2.5,2.5 0 0,0 8,10.5A2.5,2.5 0 0,0 10.5,8A2.5,2.5 0 0,0 8,5.5M8,13C10.67,13 13.33,14.67 14,16.67V20H2V16.67C2.67,14.67 5.33,13 8,13Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), \"I am a:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"role-options\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                className: `role-option ${formData.role === 'patient' ? 'active' : ''}`,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setFormData({\n                  ...formData,\n                  role: 'patient'\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    id: \"patient\",\n                    name: \"role\",\n                    value: \"patient\",\n                    checked: formData.role === 'patient',\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"patient\",\n                    children: \"Patient\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"role-description\",\n                    children: \"Seeking medical care\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: `role-option ${formData.role === 'doctor' ? 'active' : ''}`,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setFormData({\n                  ...formData,\n                  role: 'doctor'\n                }),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"role-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    id: \"doctor\",\n                    name: \"role\",\n                    value: \"doctor\",\n                    checked: formData.role === 'doctor',\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"doctor\",\n                    children: \"Doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"role-description\",\n                    children: \"Healthcare provider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: `auth-button ${isLoading ? 'loading' : ''}`,\n            whileHover: {\n              scale: isLoading ? 1 : 1.03\n            },\n            whileTap: {\n              scale: isLoading ? 1 : 0.98\n            },\n            disabled: isLoading,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), \"Creating Account...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"button-icon\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), \"Create Account\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-redirect\",\n          children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"UtUJfDBITmx/Cqjl7KlRCQGUxIA=\");\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "motion", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "role", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isLoading", "setIsLoading", "passwordStrength", "setPasswordStrength", "handleChange", "e", "value", "target", "calculatePasswordStrength", "strength", "length", "test", "handleSubmit", "preventDefault", "setTimeout", "console", "log", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "ease", "y", "delay", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "rotate", "repeat", "Infinity", "form", "onSubmit", "type", "id", "onChange", "placeholder", "required", "htmlFor", "onClick", "style", "width", "whileHover", "whileTap", "checked", "button", "disabled", "href", "_c", "$RefreshReg$"], "sources": ["E:/projects/doctorly/src/components/Auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport '../../styles/fonts.css';\r\nimport '../../styles/colors.css';\r\nimport '../../styles/auth.css';\r\n\r\nconst Register = () => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    role: 'patient'\r\n  });\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [passwordStrength, setPasswordStrength] = useState(0);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n\r\n    // Calculate password strength\r\n    if (name === 'password') {\r\n      calculatePasswordStrength(value);\r\n    }\r\n  };\r\n\r\n  const calculatePasswordStrength = (password) => {\r\n    let strength = 0;\r\n    if (password.length >= 8) strength += 1;\r\n    if (/[A-Z]/.test(password)) strength += 1;\r\n    if (/[a-z]/.test(password)) strength += 1;\r\n    if (/[0-9]/.test(password)) strength += 1;\r\n    if (/[^A-Za-z0-9]/.test(password)) strength += 1;\r\n    setPasswordStrength(strength);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    // Simulate loading\r\n    setTimeout(() => {\r\n      setIsLoading(false);\r\n      console.log('Registration data:', formData);\r\n    }, 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-card register-card glass-strong\">\r\n        <motion.div\r\n          className=\"auth-left bg-gradient-aurora\"\r\n          initial={{ opacity: 0, x: -50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n        >\r\n          <motion.div\r\n            className=\"auth-logo\"\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n          >\r\n            <div className=\"logo-icon glass\">\r\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n              </svg>\r\n            </div>\r\n            <h2 className=\"logo-text text-inverse\">Doctorly</h2>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"auth-illustration\"\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n          >\r\n            <div className=\"registration-scene\">\r\n              <div className=\"floating-elements\">\r\n                <motion.div\r\n                  className=\"medical-card card-1\"\r\n                  animate={{\r\n                    y: [0, -12, 0],\r\n                    rotate: [0, 3, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 5,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\"\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-2\"\r\n                  animate={{\r\n                    y: [0, -18, 0],\r\n                    rotate: [0, -2, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 6,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 1.5\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className=\"medical-card card-3\"\r\n                  animate={{\r\n                    y: [0, -10, 0],\r\n                    rotate: [0, 1, 0]\r\n                  }}\r\n                  transition={{\r\n                    duration: 7,\r\n                    repeat: Infinity,\r\n                    ease: \"easeInOut\",\r\n                    delay: 3\r\n                  }}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M9,11H7V9H9V11M13,11H11V9H13V11M17,11H15V9H17V11M19,3H18V1H16V3H8V1H6V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z\"/>\r\n                  </svg>\r\n                </motion.div>\r\n              </div>\r\n\r\n              <div className=\"pulse-rings\">\r\n                <div className=\"pulse-ring ring-1\"></div>\r\n                <div className=\"pulse-ring ring-2\"></div>\r\n                <div className=\"pulse-ring ring-3\"></div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.6 }}\r\n          >\r\n            <p className=\"auth-tagline body-large text-inverse text-shadow-md\">\r\n              Join us for better healthcare\r\n            </p>\r\n            <div className=\"tagline-accent\"></div>\r\n          </motion.div>\r\n        </motion.div>\r\n        \r\n        <motion.div\r\n          className=\"auth-form-container\"\r\n          initial={{ opacity: 0, x: 50 }}\r\n          animate={{ opacity: 1, x: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.3, ease: \"easeOut\" }}\r\n        >\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.5 }}\r\n          >\r\n            <h1 className=\"heading-1 text-primary\">Create Account</h1>\r\n            <p className=\"auth-subtitle body-large text-secondary\">\r\n              Join thousands of users for better healthcare\r\n            </p>\r\n          </motion.div>\r\n          \r\n          <motion.form\r\n            onSubmit={handleSubmit}\r\n            className=\"auth-form register-form\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.7 }}\r\n          >\r\n            <div className=\"form-row\">\r\n              <motion.div\r\n                className=\"floating-input-group\"\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.5, delay: 0.8 }}\r\n              >\r\n                <div className=\"input-container\">\r\n                  <div className=\"input-icon-wrapper\">\r\n                    <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    className=\"floating-input focus-primary color-transition\"\r\n                    placeholder=\" \"\r\n                    required\r\n                  />\r\n                  <label htmlFor=\"name\" className=\"floating-label label text-secondary\">\r\n                    Full Name\r\n                  </label>\r\n                  <div className=\"input-border\"></div>\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            <motion.div\r\n              className=\"floating-input-group\"\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.9 }}\r\n            >\r\n              <div className=\"input-container\">\r\n                <div className=\"input-icon-wrapper\">\r\n                  <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z\"/>\r\n                  </svg>\r\n                </div>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"floating-input focus-primary color-transition\"\r\n                  placeholder=\" \"\r\n                  required\r\n                />\r\n                <label htmlFor=\"email\" className=\"floating-label label text-secondary\">\r\n                  Email Address\r\n                </label>\r\n                <div className=\"input-border\"></div>\r\n              </div>\r\n            </motion.div>\r\n            \r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"password\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                </svg>\r\n                Password\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  value={formData.password}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Create a strong password\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"password-toggle\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              {formData.password && (\r\n                <div className=\"password-strength\">\r\n                  <div className=\"strength-bar\">\r\n                    <div\r\n                      className={`strength-fill strength-${passwordStrength}`}\r\n                      style={{ width: `${(passwordStrength / 5) * 100}%` }}\r\n                    ></div>\r\n                  </div>\r\n                  <span className={`strength-text strength-${passwordStrength}`}>\r\n                    {passwordStrength === 0 && 'Very Weak'}\r\n                    {passwordStrength === 1 && 'Weak'}\r\n                    {passwordStrength === 2 && 'Fair'}\r\n                    {passwordStrength === 3 && 'Good'}\r\n                    {passwordStrength === 4 && 'Strong'}\r\n                    {passwordStrength === 5 && 'Very Strong'}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"confirmPassword\">\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z\"/>\r\n                </svg>\r\n                Confirm Password\r\n              </label>\r\n              <div className=\"input-wrapper\">\r\n                <input\r\n                  type={showConfirmPassword ? \"text\" : \"password\"}\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  value={formData.confirmPassword}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Confirm your password\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"password-toggle\"\r\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                >\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    {showConfirmPassword ? (\r\n                      <path d=\"M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z\"/>\r\n                    ) : (\r\n                      <path d=\"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z\"/>\r\n                    )}\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              {formData.confirmPassword && formData.password !== formData.confirmPassword && (\r\n                <div className=\"password-mismatch\">\r\n                  <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\"/>\r\n                  </svg>\r\n                  Passwords do not match\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"form-group role-selection\">\r\n              <label>\r\n                <svg className=\"input-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M16,4C16.88,4 17.67,4.38 18.18,5L20.5,2.69L21.81,4L19.5,6.31C19.81,6.81 20,7.39 20,8A4,4 0 0,1 16,12A4,4 0 0,1 12,8A4,4 0 0,1 16,4M16,5.5A2.5,2.5 0 0,0 13.5,8A2.5,2.5 0 0,0 16,10.5A2.5,2.5 0 0,0 18.5,8A2.5,2.5 0 0,0 16,5.5M16,13C18.67,13 21.33,14.67 22,16.67V20H10V16.67C10.67,14.67 13.33,13 16,13M8,12A4,4 0 0,1 4,8A4,4 0 0,1 8,4A4,4 0 0,1 12,8A4,4 0 0,1 8,12M8,5.5A2.5,2.5 0 0,0 5.5,8A2.5,2.5 0 0,0 8,10.5A2.5,2.5 0 0,0 10.5,8A2.5,2.5 0 0,0 8,5.5M8,13C10.67,13 13.33,14.67 14,16.67V20H2V16.67C2.67,14.67 5.33,13 8,13Z\"/>\r\n                </svg>\r\n                I am a:\r\n              </label>\r\n              <div className=\"role-options\">\r\n                <motion.div\r\n                  className={`role-option ${formData.role === 'patient' ? 'active' : ''}`}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => setFormData({...formData, role: 'patient'})}\r\n                >\r\n                  <div className=\"role-icon\">\r\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"role-content\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"patient\"\r\n                      name=\"role\"\r\n                      value=\"patient\"\r\n                      checked={formData.role === 'patient'}\r\n                      onChange={handleChange}\r\n                    />\r\n                    <label htmlFor=\"patient\">Patient</label>\r\n                    <span className=\"role-description\">Seeking medical care</span>\r\n                  </div>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  className={`role-option ${formData.role === 'doctor' ? 'active' : ''}`}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => setFormData({...formData, role: 'doctor'})}\r\n                >\r\n                  <div className=\"role-icon\">\r\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,6V13H12.5V11.5H14.5V10H12.5V7.5H15V6H11Z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"role-content\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      id=\"doctor\"\r\n                      name=\"role\"\r\n                      value=\"doctor\"\r\n                      checked={formData.role === 'doctor'}\r\n                      onChange={handleChange}\r\n                    />\r\n                    <label htmlFor=\"doctor\">Doctor</label>\r\n                    <span className=\"role-description\">Healthcare provider</span>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n            \r\n            <motion.button\r\n              type=\"submit\"\r\n              className={`auth-button ${isLoading ? 'loading' : ''}`}\r\n              whileHover={{ scale: isLoading ? 1 : 1.03 }}\r\n              whileTap={{ scale: isLoading ? 1 : 0.98 }}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? (\r\n                <div className=\"button-content\">\r\n                  <div className=\"spinner\"></div>\r\n                  Creating Account...\r\n                </div>\r\n              ) : (\r\n                <div className=\"button-content\">\r\n                  <svg className=\"button-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                    <path d=\"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z\"/>\r\n                  </svg>\r\n                  Create Account\r\n                </div>\r\n              )}\r\n            </motion.button>\r\n          </motion.form>\r\n          \r\n          <p className=\"auth-redirect\">\r\n            Already have an account? <a href=\"/login\">Sign in</a>\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Register;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAO,wBAAwB;AAC/B,OAAO,yBAAyB;AAChC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC;IACvCQ,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEd,IAAI;MAAEe;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACE,IAAI,GAAGe;IACV,CAAC,CAAC;;IAEF;IACA,IAAIf,IAAI,KAAK,UAAU,EAAE;MACvBiB,yBAAyB,CAACF,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAME,yBAAyB,GAAIf,QAAQ,IAAK;IAC9C,IAAIgB,QAAQ,GAAG,CAAC;IAChB,IAAIhB,QAAQ,CAACiB,MAAM,IAAI,CAAC,EAAED,QAAQ,IAAI,CAAC;IACvC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,OAAO,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IACzC,IAAI,cAAc,CAACE,IAAI,CAAClB,QAAQ,CAAC,EAAEgB,QAAQ,IAAI,CAAC;IAChDN,mBAAmB,CAACM,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBZ,YAAY,CAAC,IAAI,CAAC;IAClB;IACAa,UAAU,CAAC,MAAM;MACfb,YAAY,CAAC,KAAK,CAAC;MACnBc,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE3B,QAAQ,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEH,OAAA;IAAK+B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BhC,OAAA;MAAK+B,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDhC,OAAA,CAACF,MAAM,CAACmC,GAAG;QACTF,SAAS,EAAC,8BAA8B;QACxCG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAAAR,QAAA,gBAE/ChC,OAAA,CAACF,MAAM,CAACmC,GAAG;UACTF,SAAS,EAAC,WAAW;UACrBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ChC,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhC,OAAA;cAAK2C,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAZ,QAAA,eAC1ChC,OAAA;gBAAM6C,CAAC,EAAC;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAI+B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;UACTF,SAAS,EAAC,mBAAmB;UAC7BG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAI,CAAE;UACpCb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAE,CAAE;UAClCZ,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,eAE1ChC,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChC,OAAA;cAAK+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChC,OAAA,CAACF,MAAM,CAACmC,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE;gBACR,CAAE;gBAAAR,QAAA,eAEFhC,OAAA;kBAAK2C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ChC,OAAA;oBAAM6C,CAAC,EAAC;kBAAgI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;gBACnB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFhC,OAAA;kBAAK2C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ChC,OAAA;oBAAM6C,CAAC,EAAC;kBAA2C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;gBACTF,SAAS,EAAC,qBAAqB;gBAC/BM,OAAO,EAAE;kBACPI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdU,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFb,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXa,MAAM,EAAEC,QAAQ;kBAChBb,IAAI,EAAE,WAAW;kBACjBE,KAAK,EAAE;gBACT,CAAE;gBAAAV,QAAA,eAEFhC,OAAA;kBAAK2C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eAC1ChC,OAAA;oBAAM6C,CAAC,EAAC;kBAAmK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENjD,OAAA;cAAK+B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhC,OAAA;gBAAK+B,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCjD,OAAA;gBAAK+B,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCjD,OAAA;gBAAK+B,SAAS,EAAC;cAAmB;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ChC,OAAA;YAAG+B,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAAC;UAEnE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjD,OAAA;YAAK+B,SAAS,EAAC;UAAgB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;QACTF,SAAS,EAAC,qBAAqB;QAC/BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE,GAAG;UAAEF,IAAI,EAAE;QAAU,CAAE;QAAAR,QAAA,gBAE3DhC,OAAA,CAACF,MAAM,CAACmC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ChC,OAAA;YAAI+B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAc;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1DjD,OAAA;YAAG+B,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACwD,IAAI;UACVC,QAAQ,EAAE7B,YAAa;UACvBK,SAAS,EAAC,yBAAyB;UACnCG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAE1ChC,OAAA;YAAK+B,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBhC,OAAA,CAACF,MAAM,CAACmC,GAAG;cACTF,SAAS,EAAC,sBAAsB;cAChCG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE;cAAI,CAAE;cAAAV,QAAA,eAE1ChC,OAAA;gBAAK+B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BhC,OAAA;kBAAK+B,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjChC,OAAA;oBAAK+B,SAAS,EAAC,YAAY;oBAACY,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAZ,QAAA,eACjEhC,OAAA;sBAAM6C,CAAC,EAAC;oBAAgI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjD,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,MAAM;kBACTpD,IAAI,EAAC,MAAM;kBACXe,KAAK,EAAEjB,QAAQ,CAACE,IAAK;kBACrBqD,QAAQ,EAAExC,YAAa;kBACvBa,SAAS,EAAC,+CAA+C;kBACzD4B,WAAW,EAAC,GAAG;kBACfC,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACFjD,OAAA;kBAAO6D,OAAO,EAAC,MAAM;kBAAC9B,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAEtE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjD,OAAA;kBAAK+B,SAAS,EAAC;gBAAc;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENjD,OAAA,CAACF,MAAM,CAACmC,GAAG;YACTF,SAAS,EAAC,sBAAsB;YAChCG,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAAAV,QAAA,eAE1ChC,OAAA;cAAK+B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhC,OAAA;gBAAK+B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjChC,OAAA;kBAAK+B,SAAS,EAAC,YAAY;kBAACY,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,eACjEhC,OAAA;oBAAM6C,CAAC,EAAC;kBAAmH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjD,OAAA;gBACEwD,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACVpD,IAAI,EAAC,OAAO;gBACZe,KAAK,EAAEjB,QAAQ,CAACG,KAAM;gBACtBoD,QAAQ,EAAExC,YAAa;gBACvBa,SAAS,EAAC,+CAA+C;gBACzD4B,WAAW,EAAC,GAAG;gBACfC,QAAQ;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFjD,OAAA;gBAAO6D,OAAO,EAAC,OAAO;gBAAC9B,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEvE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjD,OAAA;gBAAK+B,SAAS,EAAC;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbjD,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhC,OAAA;cAAO6D,OAAO,EAAC,UAAU;cAAA7B,QAAA,gBACvBhC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eACjEhC,OAAA;kBAAM6C,CAAC,EAAC;gBAA6O;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpP,CAAC,YAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhC,OAAA;gBACEwD,IAAI,EAAE9C,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC+C,EAAE,EAAC,UAAU;gBACbpD,IAAI,EAAC,UAAU;gBACfe,KAAK,EAAEjB,QAAQ,CAACI,QAAS;gBACzBmD,QAAQ,EAAExC,YAAa;gBACvByC,WAAW,EAAC,0BAA0B;gBACtCC,QAAQ;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFjD,OAAA;gBACEwD,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,iBAAiB;gBAC3B+B,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAsB,QAAA,eAE9ChC,OAAA;kBAAK2C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,EACzCtB,YAAY,gBACXV,OAAA;oBAAM6C,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBjD,OAAA;oBAAM6C,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL9C,QAAQ,CAACI,QAAQ,iBAChBP,OAAA;cAAK+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BhC,OAAA;kBACE+B,SAAS,EAAE,0BAA0Bf,gBAAgB,EAAG;kBACxD+C,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAIhD,gBAAgB,GAAG,CAAC,GAAI,GAAG;kBAAI;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjD,OAAA;gBAAM+B,SAAS,EAAE,0BAA0Bf,gBAAgB,EAAG;gBAAAgB,QAAA,GAC3DhB,gBAAgB,KAAK,CAAC,IAAI,WAAW,EACrCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,MAAM,EAChCA,gBAAgB,KAAK,CAAC,IAAI,QAAQ,EAClCA,gBAAgB,KAAK,CAAC,IAAI,aAAa;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjD,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhC,OAAA;cAAO6D,OAAO,EAAC,iBAAiB;cAAA7B,QAAA,gBAC9BhC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eACjEhC,OAAA;kBAAM6C,CAAC,EAAC;gBAA6O;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpP,CAAC,oBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhC,OAAA;gBACEwD,IAAI,EAAE5C,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChD6C,EAAE,EAAC,iBAAiB;gBACpBpD,IAAI,EAAC,iBAAiB;gBACtBe,KAAK,EAAEjB,QAAQ,CAACK,eAAgB;gBAChCkD,QAAQ,EAAExC,YAAa;gBACvByC,WAAW,EAAC,uBAAuB;gBACnCC,QAAQ;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFjD,OAAA;gBACEwD,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,iBAAiB;gBAC3B+B,OAAO,EAAEA,CAAA,KAAMjD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAAAoB,QAAA,eAE5DhC,OAAA;kBAAK2C,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,cAAc;kBAAAZ,QAAA,EACzCpB,mBAAmB,gBAClBZ,OAAA;oBAAM6C,CAAC,EAAC;kBAAkkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAE5kBjD,OAAA;oBAAM6C,CAAC,EAAC;kBAAmP;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAC7P;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL9C,QAAQ,CAACK,eAAe,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,iBACzER,OAAA;cAAK+B,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChC,OAAA;gBAAK2C,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eAC1ChC,OAAA;kBAAM6C,CAAC,EAAC;gBAA2G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,0BAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjD,OAAA;YAAK+B,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxChC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAK+B,SAAS,EAAC,YAAY;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eACjEhC,OAAA;kBAAM6C,CAAC,EAAC;gBAAygB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChhB,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjD,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA,CAACF,MAAM,CAACmC,GAAG;gBACTF,SAAS,EAAE,eAAe5B,QAAQ,CAACM,IAAI,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACxEwD,UAAU,EAAE;kBAAEf,KAAK,EAAE;gBAAK,CAAE;gBAC5BgB,QAAQ,EAAE;kBAAEhB,KAAK,EAAE;gBAAK,CAAE;gBAC1BY,OAAO,EAAEA,CAAA,KAAM1D,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEM,IAAI,EAAE;gBAAS,CAAC,CAAE;gBAAAuB,QAAA,gBAE3DhC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBhC,OAAA;oBAAK2C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAZ,QAAA,eAC1ChC,OAAA;sBAAM6C,CAAC,EAAC;oBAAgI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjD,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BhC,OAAA;oBACEwD,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,SAAS;oBACZpD,IAAI,EAAC,MAAM;oBACXe,KAAK,EAAC,SAAS;oBACf+C,OAAO,EAAEhE,QAAQ,CAACM,IAAI,KAAK,SAAU;oBACrCiD,QAAQ,EAAExC;kBAAa;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFjD,OAAA;oBAAO6D,OAAO,EAAC,SAAS;oBAAA7B,QAAA,EAAC;kBAAO;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxCjD,OAAA;oBAAM+B,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbjD,OAAA,CAACF,MAAM,CAACmC,GAAG;gBACTF,SAAS,EAAE,eAAe5B,QAAQ,CAACM,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACvEwD,UAAU,EAAE;kBAAEf,KAAK,EAAE;gBAAK,CAAE;gBAC5BgB,QAAQ,EAAE;kBAAEhB,KAAK,EAAE;gBAAK,CAAE;gBAC1BY,OAAO,EAAEA,CAAA,KAAM1D,WAAW,CAAC;kBAAC,GAAGD,QAAQ;kBAAEM,IAAI,EAAE;gBAAQ,CAAC,CAAE;gBAAAuB,QAAA,gBAE1DhC,OAAA;kBAAK+B,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBhC,OAAA;oBAAK2C,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAAZ,QAAA,eAC1ChC,OAAA;sBAAM6C,CAAC,EAAC;oBAA4L;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjD,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BhC,OAAA;oBACEwD,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,QAAQ;oBACXpD,IAAI,EAAC,MAAM;oBACXe,KAAK,EAAC,QAAQ;oBACd+C,OAAO,EAAEhE,QAAQ,CAACM,IAAI,KAAK,QAAS;oBACpCiD,QAAQ,EAAExC;kBAAa;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFjD,OAAA;oBAAO6D,OAAO,EAAC,QAAQ;oBAAA7B,QAAA,EAAC;kBAAM;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtCjD,OAAA;oBAAM+B,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA,CAACF,MAAM,CAACsE,MAAM;YACZZ,IAAI,EAAC,QAAQ;YACbzB,SAAS,EAAE,eAAejB,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YACvDmD,UAAU,EAAE;cAAEf,KAAK,EAAEpC,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC5CoD,QAAQ,EAAE;cAAEhB,KAAK,EAAEpC,SAAS,GAAG,CAAC,GAAG;YAAK,CAAE;YAC1CuD,QAAQ,EAAEvD,SAAU;YAAAkB,QAAA,EAEnBlB,SAAS,gBACRd,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhC,OAAA;gBAAK+B,SAAS,EAAC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,uBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAENjD,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhC,OAAA;gBAAK+B,SAAS,EAAC,aAAa;gBAACY,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAZ,QAAA,eAClEhC,OAAA;kBAAM6C,CAAC,EAAC;gBAA2C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,kBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEdjD,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,2BACF,eAAAhC,OAAA;YAAGsE,IAAI,EAAC,QAAQ;YAAAtC,QAAA,EAAC;UAAO;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAjaID,QAAQ;AAAAsE,EAAA,GAARtE,QAAQ;AAmad,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}